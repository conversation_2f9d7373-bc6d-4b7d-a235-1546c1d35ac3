from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `ai_model_configs` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `name` VARCHAR(100) NOT NULL COMMENT '模型名称',
    `model_type` VARCHAR(10) NOT NULL COMMENT '模型类型',
    `api_key` VARCHAR(500) NOT NULL COMMENT 'API密钥',
    `api_url` VARCHAR(500) COMMENT 'API地址',
    `model_name` VARCHAR(100) COMMENT '具体模型名称',
    `max_tokens` INT NOT NULL COMMENT '最大token数' DEFAULT 4096,
    `temperature` DOUBLE NOT NULL COMMENT '温度参数' DEFAULT 0.7,
    `timeout` INT NOT NULL COMMENT '超时时间(秒)' DEFAULT 30,
    `status` VARCHAR(8) NOT NULL COMMENT '状态' DEFAULT 'inactive',
    `is_default` BOOL NOT NULL COMMENT '是否默认模型' DEFAULT 0,
    `description` LONGTEXT COMMENT '模型描述',
    `config_json` JSON COMMENT '额外配置参数(JSON格式)',
    `user_id` INT NOT NULL COMMENT '创建用户ID',
    KEY `idx_ai_model_co_created_4ba46b` (`created_at`),
    KEY `idx_ai_model_co_updated_c9d346` (`updated_at`),
    KEY `idx_ai_model_co_name_3f0aae` (`name`),
    KEY `idx_ai_model_co_model_t_ca70fd` (`model_type`),
    KEY `idx_ai_model_co_status_64adfe` (`status`),
    KEY `idx_ai_model_co_is_defa_f5ff3d` (`is_default`),
    KEY `idx_ai_model_co_user_id_47150c` (`user_id`)
) CHARACTER SET utf8mb4 COMMENT='AI大模型配置表';
CREATE TABLE IF NOT EXISTS `ai_test_case_generations` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `task_name` VARCHAR(200) NOT NULL COMMENT '任务名称',
    `requirement_description` LONGTEXT NOT NULL COMMENT '需求描述',
    `prompt_template_id` INT COMMENT '使用的提示词模板ID',
    `ai_model_config_id` INT NOT NULL COMMENT '使用的AI模型配置ID',
    `project_id` INT NOT NULL COMMENT '所属项目ID',
    `module_id` INT COMMENT '所属模块ID',
    `generated_count` INT NOT NULL COMMENT '生成的测试用例数量' DEFAULT 0,
    `generated_cases` JSON COMMENT '生成的测试用例内容（JSON格式）',
    `status` VARCHAR(20) NOT NULL COMMENT '生成状态：pending-待生成，generating-生成中，completed-已完成，failed-失败' DEFAULT 'pending',
    `error_message` LONGTEXT COMMENT '错误信息',
    `generation_time` INT COMMENT '生成耗时(秒)',
    `user_id` INT NOT NULL COMMENT '创建用户ID',
    KEY `idx_ai_test_cas_created_b8242e` (`created_at`),
    KEY `idx_ai_test_cas_updated_6d8d52` (`updated_at`),
    KEY `idx_ai_test_cas_task_na_a7a635` (`task_name`),
    KEY `idx_ai_test_cas_prompt__818460` (`prompt_template_id`),
    KEY `idx_ai_test_cas_ai_mode_0fe884` (`ai_model_config_id`),
    KEY `idx_ai_test_cas_project_b4e57b` (`project_id`),
    KEY `idx_ai_test_cas_module__db5b9e` (`module_id`),
    KEY `idx_ai_test_cas_status_286003` (`status`),
    KEY `idx_ai_test_cas_user_id_b9b0b4` (`user_id`)
) CHARACTER SET utf8mb4 COMMENT='AI测试用例生成记录表';
CREATE TABLE IF NOT EXISTS `api` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `path` VARCHAR(100) NOT NULL COMMENT 'API路径',
    `method` VARCHAR(7) NOT NULL COMMENT '请求方法',
    `summary` VARCHAR(500) NOT NULL COMMENT '请求简介',
    `tags` VARCHAR(100) NOT NULL COMMENT 'API标签',
    KEY `idx_api_created_78d19f` (`created_at`),
    KEY `idx_api_updated_643c8b` (`updated_at`),
    KEY `idx_api_path_9ed611` (`path`),
    KEY `idx_api_method_a46dfb` (`method`),
    KEY `idx_api_summary_400f73` (`summary`),
    KEY `idx_api_tags_04ae27` (`tags`)
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `api_execution_history` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `user_id` INT NOT NULL COMMENT '用户ID',
    `method` VARCHAR(7) NOT NULL COMMENT '请求方法',
    `url` VARCHAR(500) NOT NULL COMMENT '请求URL',
    `params` JSON COMMENT '请求参数',
    `headers` JSON COMMENT '请求头',
    `body` LONGTEXT COMMENT '请求体',
    `status_code` INT COMMENT '响应状态码',
    `response_time` INT COMMENT '响应时间(毫秒)',
    `success` BOOL NOT NULL COMMENT '是否成功' DEFAULT 1,
    `response_headers` JSON COMMENT '响应头',
    `response_body` LONGTEXT COMMENT '响应体',
    `response_size` VARCHAR(50) COMMENT '响应大小',
    `response_status_text` VARCHAR(100) COMMENT '响应状态文本',
    KEY `idx_api_executi_created_6cd6df` (`created_at`),
    KEY `idx_api_executi_updated_a9ccbc` (`updated_at`),
    KEY `idx_api_executi_user_id_a6721a` (`user_id`),
    KEY `idx_api_executi_method_cd2f94` (`method`)
) CHARACTER SET utf8mb4 COMMENT='API执行历史记录表';
CREATE TABLE IF NOT EXISTS `api_import` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `api_name` VARCHAR(100) NOT NULL COMMENT '接口名称说明',
    `url_path` VARCHAR(500) NOT NULL COMMENT '接口路径',
    `method` VARCHAR(7) NOT NULL COMMENT '请求方式',
    `params_list` LONGTEXT COMMENT '请求参数',
    `status` VARCHAR(20) NOT NULL COMMENT '状态',
    `project_id` INT COMMENT '所属项目ID',
    KEY `idx_api_import_created_7ae5fa` (`created_at`),
    KEY `idx_api_import_updated_fb0e29` (`updated_at`),
    KEY `idx_api_import_api_nam_ea8d9b` (`api_name`),
    KEY `idx_api_import_url_pat_bef211` (`url_path`),
    KEY `idx_api_import_method_c7cf80` (`method`),
    KEY `idx_api_import_project_916826` (`project_id`)
) CHARACTER SET utf8mb4 COMMENT='API导入';
CREATE TABLE IF NOT EXISTS `api_request` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `api_name` VARCHAR(100) NOT NULL COMMENT '接口名称',
    `url` VARCHAR(500) NOT NULL COMMENT '请求URL',
    `method` VARCHAR(7) NOT NULL COMMENT '请求方法',
    `params` JSON COMMENT '请求参数',
    `headers` JSON COMMENT '请求头',
    `body` LONGTEXT COMMENT '请求体',
    `description` LONGTEXT COMMENT '接口描述',
    `user_id` INT COMMENT '创建用户ID',
    `project_id` INT COMMENT '所属项目ID',
    `module_id` INT COMMENT '所属模块ID',
    `category` VARCHAR(50) COMMENT '接口分类',
    `is_favorite` BOOL NOT NULL COMMENT '是否收藏' DEFAULT 0,
    `last_executed` DATETIME(6) COMMENT '最后执行时间',
    `execution_count` INT NOT NULL COMMENT '执行次数' DEFAULT 0,
    KEY `idx_api_request_created_d93ff8` (`created_at`),
    KEY `idx_api_request_updated_1d7eca` (`updated_at`),
    KEY `idx_api_request_api_nam_1e0cfb` (`api_name`),
    KEY `idx_api_request_url_7e6420` (`url`),
    KEY `idx_api_request_method_bfa1f5` (`method`),
    KEY `idx_api_request_user_id_5d578d` (`user_id`),
    KEY `idx_api_request_project_643f07` (`project_id`),
    KEY `idx_api_request_module__b44991` (`module_id`),
    KEY `idx_api_request_categor_93c3ea` (`category`),
    KEY `idx_api_request_is_favo_021029` (`is_favorite`),
    KEY `idx_api_request_last_ex_f6653a` (`last_executed`)
) CHARACTER SET utf8mb4 COMMENT='API请求记录表';
CREATE TABLE IF NOT EXISTS `api_test_cases` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `case_number` VARCHAR(50) NOT NULL UNIQUE COMMENT '用例编号',
    `case_name` VARCHAR(200) NOT NULL COMMENT '用例名称',
    `method` VARCHAR(7) NOT NULL COMMENT '请求方式',
    `url` VARCHAR(500) NOT NULL COMMENT '请求URL',
    `params` JSON COMMENT '请求参数',
    `body` LONGTEXT COMMENT '请求体',
    `expected_result` LONGTEXT COMMENT '断言配置（JSON格式）',
    `variable_extracts` LONGTEXT COMMENT '变量提取配置（JSON格式）',
    `is_smoke` BOOL NOT NULL COMMENT '是否冒烟用例' DEFAULT 0,
    `status` VARCHAR(20) NOT NULL COMMENT '状态：pending-待审核，approved-已审核' DEFAULT 'pending',
    `source` VARCHAR(20) NOT NULL COMMENT '来源：manual-人工，ai-AI' DEFAULT 'manual',
    `project_id` INT NOT NULL COMMENT '所属项目ID',
    `module_id` INT COMMENT '所属模块ID',
    `user_id` INT COMMENT '创建用户ID',
    KEY `idx_api_test_ca_created_16492a` (`created_at`),
    KEY `idx_api_test_ca_updated_6089c9` (`updated_at`),
    KEY `idx_api_test_ca_case_nu_c3002d` (`case_number`),
    KEY `idx_api_test_ca_case_na_e603e4` (`case_name`),
    KEY `idx_api_test_ca_method_823cbe` (`method`),
    KEY `idx_api_test_ca_is_smok_366138` (`is_smoke`),
    KEY `idx_api_test_ca_status_21de97` (`status`),
    KEY `idx_api_test_ca_source_1b953b` (`source`),
    KEY `idx_api_test_ca_project_26f45a` (`project_id`),
    KEY `idx_api_test_ca_module__683bc8` (`module_id`),
    KEY `idx_api_test_ca_user_id_2326a5` (`user_id`)
) CHARACTER SET utf8mb4 COMMENT='接口测试用例表';
CREATE TABLE IF NOT EXISTS `api_test_plans` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `plan_name` VARCHAR(200) NOT NULL COMMENT '计划名称',
    `level` VARCHAR(10) NOT NULL COMMENT '等级：low-低，medium-中，high-高' DEFAULT 'medium',
    `status` VARCHAR(20) NOT NULL COMMENT '状态：not_started-未开始，in_progress-进行中，completed-已完成' DEFAULT 'not_started',
    `description` LONGTEXT COMMENT '计划描述',
    `project_id` INT NOT NULL COMMENT '所属项目ID',
    `environment_id` INT COMMENT '运行环境ID',
    `execution_result` VARCHAR(20) COMMENT '执行结果：success-成功，failed-失败，running-执行中',
    `pass_rate` DOUBLE COMMENT '通过率',
    `last_execution_time` DATETIME(6) COMMENT '最近执行时间',
    `user_id` INT NOT NULL COMMENT '创建用户ID',
    `enable_dingtalk` BOOL NOT NULL COMMENT '是否启用钉钉通知' DEFAULT 0,
    KEY `idx_api_test_pl_created_27c6bc` (`created_at`),
    KEY `idx_api_test_pl_updated_0425ee` (`updated_at`),
    KEY `idx_api_test_pl_plan_na_6628ba` (`plan_name`),
    KEY `idx_api_test_pl_level_73dd05` (`level`),
    KEY `idx_api_test_pl_status_9108e5` (`status`),
    KEY `idx_api_test_pl_project_a05296` (`project_id`),
    KEY `idx_api_test_pl_environ_5e8bc1` (`environment_id`),
    KEY `idx_api_test_pl_executi_1bac5a` (`execution_result`),
    KEY `idx_api_test_pl_last_ex_986954` (`last_execution_time`),
    KEY `idx_api_test_pl_user_id_7df334` (`user_id`),
    KEY `idx_api_test_pl_enable__95ea71` (`enable_dingtalk`)
) CHARACTER SET utf8mb4 COMMENT='接口测试计划表';
CREATE TABLE IF NOT EXISTS `api_test_plan_cases` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `plan_id` INT NOT NULL COMMENT '测试计划ID',
    `case_id` INT NOT NULL COMMENT '测试用例ID',
    `execution_order` INT NOT NULL COMMENT '执行顺序' DEFAULT 0,
    `execution_status` VARCHAR(20) NOT NULL COMMENT '执行状态：normal-正常，skipped-跳过' DEFAULT 'normal',
    `execution_result` VARCHAR(20) COMMENT '执行结果：success-成功，failed-失败',
    `execution_time` DATETIME(6) COMMENT '执行时间',
    `response_time` INT COMMENT '响应时间(毫秒)',
    `error_message` LONGTEXT COMMENT '错误信息',
    `status_code` INT COMMENT '响应状态码',
    `response_headers` JSON COMMENT '响应头',
    `response_body` LONGTEXT COMMENT '响应体',
    `assertion_results` JSON COMMENT '断言结果',
    UNIQUE KEY `uid_api_test_pl_plan_id_178dac` (`plan_id`, `case_id`),
    KEY `idx_api_test_pl_created_426f10` (`created_at`),
    KEY `idx_api_test_pl_updated_8fa40f` (`updated_at`),
    KEY `idx_api_test_pl_plan_id_122471` (`plan_id`),
    KEY `idx_api_test_pl_case_id_fcfb16` (`case_id`),
    KEY `idx_api_test_pl_executi_01f66d` (`execution_status`),
    KEY `idx_api_test_pl_executi_48afe5` (`execution_result`)
) CHARACTER SET utf8mb4 COMMENT='接口测试计划用例关联表';
CREATE TABLE IF NOT EXISTS `auditlog` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `user_id` INT NOT NULL COMMENT '用户ID',
    `username` VARCHAR(64) NOT NULL COMMENT '用户名称' DEFAULT '',
    `module` VARCHAR(64) NOT NULL COMMENT '功能模块' DEFAULT '',
    `summary` VARCHAR(128) NOT NULL COMMENT '请求描述' DEFAULT '',
    `method` VARCHAR(10) NOT NULL COMMENT '请求方法' DEFAULT '',
    `path` VARCHAR(255) NOT NULL COMMENT '请求路径' DEFAULT '',
    `status` INT NOT NULL COMMENT '状态码' DEFAULT -1,
    `response_time` INT NOT NULL COMMENT '响应时间(单位ms)' DEFAULT 0,
    KEY `idx_auditlog_created_cc33d0` (`created_at`),
    KEY `idx_auditlog_updated_2f871f` (`updated_at`),
    KEY `idx_auditlog_user_id_4b93fa` (`user_id`),
    KEY `idx_auditlog_usernam_b187b3` (`username`),
    KEY `idx_auditlog_module_04058b` (`module`),
    KEY `idx_auditlog_summary_3e27da` (`summary`),
    KEY `idx_auditlog_method_4270a2` (`method`),
    KEY `idx_auditlog_path_b99502` (`path`),
    KEY `idx_auditlog_status_2a72d2` (`status`),
    KEY `idx_auditlog_respons_8caa87` (`response_time`)
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `database_connections` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `name` VARCHAR(100) NOT NULL COMMENT '连接名称',
    `db_type` VARCHAR(20) NOT NULL COMMENT '数据库类型：mysql,postgresql,sqlite,oracle',
    `host` VARCHAR(255) NOT NULL COMMENT '主机地址',
    `port` INT NOT NULL COMMENT '端口号',
    `database` VARCHAR(100) NOT NULL COMMENT '数据库名称',
    `username` VARCHAR(100) NOT NULL COMMENT '用户名',
    `password` VARCHAR(255) NOT NULL COMMENT '密码',
    `charset` VARCHAR(20) NOT NULL COMMENT '字符集' DEFAULT 'utf8mb4',
    `description` LONGTEXT COMMENT '连接描述',
    `is_active` BOOL NOT NULL COMMENT '是否启用' DEFAULT 1,
    `max_connections` INT NOT NULL COMMENT '最大连接数' DEFAULT 10,
    `connection_timeout` INT NOT NULL COMMENT '连接超时时间(秒)' DEFAULT 30,
    `user_id` INT NOT NULL COMMENT '创建用户ID',
    KEY `idx_database_co_created_50bca3` (`created_at`),
    KEY `idx_database_co_updated_47678d` (`updated_at`),
    KEY `idx_database_co_name_820b43` (`name`),
    KEY `idx_database_co_db_type_9e3f8c` (`db_type`),
    KEY `idx_database_co_is_acti_9ccda7` (`is_active`),
    KEY `idx_database_co_user_id_cb7667` (`user_id`)
) CHARACTER SET utf8mb4 COMMENT='数据库连接配置表';
CREATE TABLE IF NOT EXISTS `dept` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `name` VARCHAR(20) NOT NULL UNIQUE COMMENT '部门名称',
    `desc` VARCHAR(500) COMMENT '备注',
    `is_deleted` BOOL NOT NULL COMMENT '软删除标记' DEFAULT 0,
    `order` INT NOT NULL COMMENT '排序' DEFAULT 0,
    `parent_id` INT NOT NULL COMMENT '父部门ID' DEFAULT 0,
    KEY `idx_dept_created_4b11cf` (`created_at`),
    KEY `idx_dept_updated_0c0bd1` (`updated_at`),
    KEY `idx_dept_name_c2b9da` (`name`),
    KEY `idx_dept_is_dele_466228` (`is_deleted`),
    KEY `idx_dept_order_ddabe1` (`order`),
    KEY `idx_dept_parent__a71a57` (`parent_id`)
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `deptclosure` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `ancestor` INT NOT NULL COMMENT '父代',
    `descendant` INT NOT NULL COMMENT '子代',
    `level` INT NOT NULL COMMENT '深度' DEFAULT 0,
    KEY `idx_deptclosure_created_96f6ef` (`created_at`),
    KEY `idx_deptclosure_updated_41fc08` (`updated_at`),
    KEY `idx_deptclosure_ancesto_fbc4ce` (`ancestor`),
    KEY `idx_deptclosure_descend_2ae8b1` (`descendant`),
    KEY `idx_deptclosure_level_ae16b2` (`level`)
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `dingtalk_configs` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `plan_id` INT NOT NULL COMMENT '测试计划ID',
    `plan_type` VARCHAR(20) NOT NULL COMMENT '计划类型：api-接口测试计划，functional-功能测试计划',
    `webhook_url` VARCHAR(500) NOT NULL COMMENT '钉钉机器人webhook地址',
    `secret` VARCHAR(200) COMMENT '钉钉机器人密钥',
    `send_condition` VARCHAR(20) NOT NULL COMMENT '发送条件：success-仅成功，failed-仅失败，all-全部' DEFAULT 'all',
    `is_enabled` BOOL NOT NULL COMMENT '是否启用' DEFAULT 1,
    `user_id` INT NOT NULL COMMENT '创建用户ID',
    KEY `idx_dingtalk_co_created_dfd602` (`created_at`),
    KEY `idx_dingtalk_co_updated_0cda55` (`updated_at`),
    KEY `idx_dingtalk_co_plan_id_1e79e2` (`plan_id`),
    KEY `idx_dingtalk_co_plan_ty_b209fa` (`plan_type`),
    KEY `idx_dingtalk_co_send_co_c535d4` (`send_condition`),
    KEY `idx_dingtalk_co_is_enab_d426a6` (`is_enabled`),
    KEY `idx_dingtalk_co_user_id_491230` (`user_id`)
) CHARACTER SET utf8mb4 COMMENT='钉钉配置表';
CREATE TABLE IF NOT EXISTS `environments` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `name` VARCHAR(100) NOT NULL COMMENT '环境名称',
    `env_type` VARCHAR(11) NOT NULL COMMENT '环境类型',
    `host` VARCHAR(255) NOT NULL COMMENT '主机地址',
    `port` INT COMMENT '端口号',
    `token` LONGTEXT COMMENT '访问令牌',
    `prefix` LONGTEXT COMMENT '令牌前缀',
    `description` LONGTEXT COMMENT '环境描述',
    `project_id` INT NOT NULL COMMENT '所属项目ID',
    `is_active` BOOL NOT NULL COMMENT '是否启用' DEFAULT 1,
    `token_url` VARCHAR(500) COMMENT 'Token获取URL',
    `enable_captcha` BOOL NOT NULL COMMENT '是否启用验证码' DEFAULT 0,
    `captcha_url` VARCHAR(500) COMMENT '验证码获取URL',
    `captcha_method` VARCHAR(10) NOT NULL COMMENT '验证码获取请求方式' DEFAULT 'GET',
    `captcha_headers` JSON COMMENT '验证码获取请求头',
    `captcha_body` JSON COMMENT '验证码获取请求体',
    `captcha_image_path` VARCHAR(200) NOT NULL COMMENT '验证码图片字段路径(JSONPath)' DEFAULT 'content.imageBase64',
    `captcha_key_path` VARCHAR(200) NOT NULL COMMENT '验证码Key字段路径(JSONPath)' DEFAULT 'content.codeKey',
    `token_method` VARCHAR(10) NOT NULL COMMENT 'Token获取请求方式' DEFAULT 'POST',
    `token_headers` JSON COMMENT 'Token获取请求头',
    `token_body` JSON COMMENT 'Token获取请求体',
    `token_field_name` VARCHAR(100) NOT NULL COMMENT 'Token字段名称' DEFAULT 'token',
    `token_field_path` VARCHAR(200) COMMENT 'Token字段路径(JSONPath)',
    `auto_refresh_token` BOOL NOT NULL COMMENT '是否自动刷新Token' DEFAULT 0,
    `token_refresh_interval` INT NOT NULL COMMENT 'Token刷新间隔(秒)' DEFAULT 3600,
    `last_token_refresh` DATETIME(6) COMMENT '最后Token刷新时间',
    KEY `idx_environment_created_395723` (`created_at`),
    KEY `idx_environment_updated_0164e1` (`updated_at`),
    KEY `idx_environment_name_d35794` (`name`),
    KEY `idx_environment_env_typ_bdbb9c` (`env_type`),
    KEY `idx_environment_host_ffa44b` (`host`),
    KEY `idx_environment_project_8f00f8` (`project_id`),
    KEY `idx_environment_is_acti_49034c` (`is_active`)
) CHARACTER SET utf8mb4 COMMENT='环境配置表';
CREATE TABLE IF NOT EXISTS `functional_test_plans` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `plan_name` VARCHAR(200) NOT NULL COMMENT '计划名称',
    `level` VARCHAR(10) NOT NULL COMMENT '等级：low-低，medium-中，high-高' DEFAULT 'medium',
    `status` VARCHAR(20) NOT NULL COMMENT '状态：not_started-未开始，in_progress-进行中，completed-已完成' DEFAULT 'not_started',
    `description` LONGTEXT COMMENT '计划描述',
    `project_id` INT NOT NULL COMMENT '所属项目ID',
    `user_id` INT NOT NULL COMMENT '创建用户ID',
    `pass_rate` DOUBLE COMMENT '通过率',
    KEY `idx_functional__created_330ae1` (`created_at`),
    KEY `idx_functional__updated_b70b41` (`updated_at`),
    KEY `idx_functional__plan_na_0c8efd` (`plan_name`),
    KEY `idx_functional__level_d4275e` (`level`),
    KEY `idx_functional__status_3544c7` (`status`),
    KEY `idx_functional__project_a1c988` (`project_id`),
    KEY `idx_functional__user_id_8b6edf` (`user_id`)
) CHARACTER SET utf8mb4 COMMENT='功能测试计划表';
CREATE TABLE IF NOT EXISTS `functional_test_plan_cases` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `plan_id` INT NOT NULL COMMENT '测试计划ID',
    `case_id` INT NOT NULL COMMENT '测试用例ID',
    `execution_order` INT NOT NULL COMMENT '执行顺序' DEFAULT 0,
    `execution_status` VARCHAR(20) NOT NULL COMMENT '执行状态：pending-待重试，passed-通过，failed-失败，blocked-阻塞，skipped-跳过' DEFAULT 'pending',
    UNIQUE KEY `uid_functional__plan_id_198fc3` (`plan_id`, `case_id`),
    KEY `idx_functional__created_2bfd56` (`created_at`),
    KEY `idx_functional__updated_b4d5f9` (`updated_at`),
    KEY `idx_functional__plan_id_705fc5` (`plan_id`),
    KEY `idx_functional__case_id_b26055` (`case_id`),
    KEY `idx_functional__executi_456ce0` (`execution_status`)
) CHARACTER SET utf8mb4 COMMENT='功能测试计划用例关联表';
CREATE TABLE IF NOT EXISTS `menu` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `name` VARCHAR(20) NOT NULL COMMENT '菜单名称',
    `remark` JSON COMMENT '保留字段',
    `menu_type` VARCHAR(7) COMMENT '菜单类型',
    `icon` VARCHAR(100) COMMENT '菜单图标',
    `path` VARCHAR(100) NOT NULL COMMENT '菜单路径',
    `order` INT COMMENT '排序' DEFAULT 0,
    `parent_id` INT NOT NULL COMMENT '父菜单ID' DEFAULT 0,
    `is_hidden` BOOL NOT NULL COMMENT '是否隐藏' DEFAULT 0,
    `component` VARCHAR(100) NOT NULL COMMENT '组件',
    `keepalive` BOOL NOT NULL COMMENT '存活' DEFAULT 1,
    `redirect` VARCHAR(100) COMMENT '重定向',
    KEY `idx_menu_created_b6922b` (`created_at`),
    KEY `idx_menu_updated_e6b0a1` (`updated_at`),
    KEY `idx_menu_name_b9b853` (`name`),
    KEY `idx_menu_path_bf95b2` (`path`),
    KEY `idx_menu_order_606068` (`order`),
    KEY `idx_menu_parent__bebd15` (`parent_id`)
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `projects` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `name` VARCHAR(100) NOT NULL COMMENT '项目名称',
    `description` LONGTEXT COMMENT '项目描述',
    `status` VARCHAR(20) NOT NULL COMMENT '项目状态',
    `start_date` DATETIME(6) COMMENT '开始日期',
    `end_date` DATETIME(6) COMMENT '结束日期',
    `manager` VARCHAR(50) COMMENT '项目经理',
    `budget` DOUBLE NOT NULL COMMENT '项目预算' DEFAULT 0,
    KEY `idx_projects_created_f282c7` (`created_at`),
    KEY `idx_projects_updated_514ed4` (`updated_at`)
) CHARACTER SET utf8mb4 COMMENT='项目表';
CREATE TABLE IF NOT EXISTS `project_modules` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `name` VARCHAR(100) NOT NULL COMMENT '模块名称',
    `description` LONGTEXT COMMENT '模块描述',
    `project_id` INT NOT NULL COMMENT '所属项目ID',
    `parent_id` INT NOT NULL COMMENT '父模块ID' DEFAULT 0,
    `order` INT NOT NULL COMMENT '排序' DEFAULT 0,
    `status` VARCHAR(20) NOT NULL COMMENT '模块状态' DEFAULT 'active',
    KEY `idx_project_mod_created_370f25` (`created_at`),
    KEY `idx_project_mod_updated_e6cf74` (`updated_at`),
    KEY `idx_project_mod_name_e4a24d` (`name`),
    KEY `idx_project_mod_project_8699e5` (`project_id`),
    KEY `idx_project_mod_parent__d40ba8` (`parent_id`),
    KEY `idx_project_mod_order_7b50ee` (`order`)
) CHARACTER SET utf8mb4 COMMENT='项目模块表';
CREATE TABLE IF NOT EXISTS `prompt_templates` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `name` VARCHAR(100) NOT NULL COMMENT '模板名称',
    `category` VARCHAR(50) NOT NULL COMMENT '模板分类',
    `description` LONGTEXT COMMENT '模板描述',
    `prompt_content` LONGTEXT NOT NULL COMMENT '提示词内容',
    `variables` JSON COMMENT '变量定义(JSON格式)',
    `is_active` BOOL NOT NULL COMMENT '是否启用' DEFAULT 1,
    `is_default` BOOL NOT NULL COMMENT '是否默认模板' DEFAULT 0,
    `usage_count` INT NOT NULL COMMENT '使用次数' DEFAULT 0,
    `user_id` INT NOT NULL COMMENT '创建用户ID',
    KEY `idx_prompt_temp_created_451e65` (`created_at`),
    KEY `idx_prompt_temp_updated_040ecd` (`updated_at`),
    KEY `idx_prompt_temp_name_923ba7` (`name`),
    KEY `idx_prompt_temp_categor_019e36` (`category`),
    KEY `idx_prompt_temp_is_acti_826637` (`is_active`),
    KEY `idx_prompt_temp_is_defa_6774f7` (`is_default`),
    KEY `idx_prompt_temp_user_id_cf5012` (`user_id`)
) CHARACTER SET utf8mb4 COMMENT='提示词模板表';
CREATE TABLE IF NOT EXISTS `query_history` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `query_name` VARCHAR(200) COMMENT '查询名称',
    `sql_content` LONGTEXT NOT NULL COMMENT 'SQL语句内容',
    `database_connection_id` INT NOT NULL COMMENT '数据库连接ID',
    `execution_time` INT COMMENT '执行时间(毫秒)',
    `affected_rows` INT COMMENT '影响行数',
    `result_count` INT COMMENT '结果行数',
    `status` VARCHAR(20) NOT NULL COMMENT '执行状态：success-成功，failed-失败',
    `error_message` LONGTEXT COMMENT '错误信息',
    `is_favorite` BOOL NOT NULL COMMENT '是否收藏' DEFAULT 0,
    `user_id` INT NOT NULL COMMENT '执行用户ID',
    KEY `idx_query_histo_created_00d967` (`created_at`),
    KEY `idx_query_histo_updated_9da728` (`updated_at`),
    KEY `idx_query_histo_query_n_cea764` (`query_name`),
    KEY `idx_query_histo_databas_22614b` (`database_connection_id`),
    KEY `idx_query_histo_status_36b925` (`status`),
    KEY `idx_query_histo_is_favo_714122` (`is_favorite`),
    KEY `idx_query_histo_user_id_58172d` (`user_id`)
) CHARACTER SET utf8mb4 COMMENT='SQL查询历史记录表';
CREATE TABLE IF NOT EXISTS `role` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `name` VARCHAR(20) NOT NULL UNIQUE COMMENT '角色名称',
    `desc` VARCHAR(500) COMMENT '角色描述',
    KEY `idx_role_created_7f5f71` (`created_at`),
    KEY `idx_role_updated_5dd337` (`updated_at`),
    KEY `idx_role_name_e5618b` (`name`)
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `scheduled_task` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `task_name` VARCHAR(200) NOT NULL COMMENT '任务名称',
    `plan_id` INT NOT NULL COMMENT '测试计划ID',
    `cron_expression` VARCHAR(100) NOT NULL COMMENT 'Cron表达式',
    `is_active` BOOL NOT NULL COMMENT '是否启用' DEFAULT 1,
    `last_run_time` DATETIME(6) COMMENT '最后执行时间',
    `next_run_time` DATETIME(6) COMMENT '下次执行时间',
    `run_count` INT NOT NULL COMMENT '执行次数' DEFAULT 0,
    `description` LONGTEXT COMMENT '任务描述',
    `creator_id` INT NOT NULL COMMENT '创建人ID',
    KEY `idx_scheduled_t_created_c5b08b` (`created_at`),
    KEY `idx_scheduled_t_updated_3d47aa` (`updated_at`),
    KEY `idx_scheduled_t_task_na_0e9823` (`task_name`),
    KEY `idx_scheduled_t_plan_id_771e9b` (`plan_id`),
    KEY `idx_scheduled_t_is_acti_a0b151` (`is_active`),
    KEY `idx_scheduled_t_creator_1d13eb` (`creator_id`)
) CHARACTER SET utf8mb4 COMMENT='定时任务表';
CREATE TABLE IF NOT EXISTS `test_cases` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `case_number` VARCHAR(50) NOT NULL UNIQUE COMMENT '用例编号',
    `case_name` VARCHAR(200) NOT NULL COMMENT '用例名称',
    `case_level` VARCHAR(10) NOT NULL COMMENT '用例等级：low-低，medium-中，high-高' DEFAULT 'medium',
    `precondition` LONGTEXT COMMENT '前置条件',
    `test_steps` LONGTEXT NOT NULL COMMENT '用例步骤',
    `expected_result` LONGTEXT NOT NULL COMMENT '预期结果',
    `is_smoke` BOOL NOT NULL COMMENT '是否冒烟用例' DEFAULT 0,
    `status` VARCHAR(20) NOT NULL COMMENT '状态：pending-待审核，approved-已审核' DEFAULT 'pending',
    `source` VARCHAR(20) NOT NULL COMMENT '来源：manual-人工，ai-AI' DEFAULT 'manual',
    `project_id` INT NOT NULL COMMENT '所属项目ID',
    `module_id` INT COMMENT '所属模块ID',
    `user_id` INT COMMENT '创建用户ID',
    KEY `idx_test_cases_created_c2f56b` (`created_at`),
    KEY `idx_test_cases_updated_44d4c2` (`updated_at`),
    KEY `idx_test_cases_case_nu_315e53` (`case_number`),
    KEY `idx_test_cases_case_na_a79fe1` (`case_name`),
    KEY `idx_test_cases_case_le_facdd3` (`case_level`),
    KEY `idx_test_cases_is_smok_7395dd` (`is_smoke`),
    KEY `idx_test_cases_status_90d111` (`status`),
    KEY `idx_test_cases_source_62b403` (`source`),
    KEY `idx_test_cases_project_5c9a88` (`project_id`),
    KEY `idx_test_cases_module__dc4165` (`module_id`),
    KEY `idx_test_cases_user_id_88ddd9` (`user_id`)
) CHARACTER SET utf8mb4 COMMENT='功能测试用例表';
CREATE TABLE IF NOT EXISTS `user` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `username` VARCHAR(20) NOT NULL UNIQUE COMMENT '用户名称',
    `alias` VARCHAR(30) COMMENT '姓名',
    `email` VARCHAR(255) NOT NULL UNIQUE COMMENT '邮箱',
    `phone` VARCHAR(20) COMMENT '电话',
    `password` VARCHAR(128) COMMENT '密码',
    `is_active` BOOL NOT NULL COMMENT '是否激活' DEFAULT 1,
    `is_superuser` BOOL NOT NULL COMMENT '是否为超级管理员' DEFAULT 0,
    `last_login` DATETIME(6) COMMENT '最后登录时间',
    `dept_id` INT COMMENT '部门ID',
    KEY `idx_user_created_b19d59` (`created_at`),
    KEY `idx_user_updated_dfdb43` (`updated_at`),
    KEY `idx_user_usernam_9987ab` (`username`),
    KEY `idx_user_alias_6f9868` (`alias`),
    KEY `idx_user_email_1b4f1c` (`email`),
    KEY `idx_user_phone_4e3ecc` (`phone`),
    KEY `idx_user_is_acti_83722a` (`is_active`),
    KEY `idx_user_is_supe_b8a218` (`is_superuser`),
    KEY `idx_user_last_lo_af118a` (`last_login`),
    KEY `idx_user_dept_id_d4490b` (`dept_id`)
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `aerich` (
    `id` INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `version` VARCHAR(255) NOT NULL,
    `app` VARCHAR(100) NOT NULL,
    `content` JSON NOT NULL
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `role_menu` (
    `role_id` BIGINT NOT NULL,
    `menu_id` BIGINT NOT NULL,
    FOREIGN KEY (`role_id`) REFERENCES `role` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`menu_id`) REFERENCES `menu` (`id`) ON DELETE CASCADE,
    UNIQUE KEY `uidx_role_menu_role_id_90801c` (`role_id`, `menu_id`)
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `role_api` (
    `role_id` BIGINT NOT NULL,
    `api_id` BIGINT NOT NULL,
    FOREIGN KEY (`role_id`) REFERENCES `role` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`api_id`) REFERENCES `api` (`id`) ON DELETE CASCADE,
    UNIQUE KEY `uidx_role_api_role_id_ba4286` (`role_id`, `api_id`)
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `user_role` (
    `user_id` BIGINT NOT NULL,
    `role_id` BIGINT NOT NULL,
    FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`role_id`) REFERENCES `role` (`id`) ON DELETE CASCADE,
    UNIQUE KEY `uidx_user_role_user_id_d0bad3` (`user_id`, `role_id`)
) CHARACTER SET utf8mb4;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
