<script setup>
import { ref, onMounted, h, nextTick, watch } from 'vue'
import {
  NLayout,
  NLayoutSider,
  NLayoutContent,
  NTree,
  NButton,
  NInput,
  NSelect,
  NSwitch,
  NTag,
  NPopconfirm,
  NTooltip,
  NModal,
  NSpin,
  NCard,
  NTabs,
  NTabPane,
  NDropdown,
  NIcon,
  NPopover,
  NSpace,
  NText,
  NForm,
  NFormItem,
  useMessage,
  useDialog
} from 'naive-ui'
import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'

import { formatDate, renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import apiTestCaseApi from '@/api/apiTestCase'
import projectApi from '@/api/project'
import environmentApi from '@/api/environment'
import TheIcon from '@/components/icon/TheIcon.vue'
import { lStorage } from '@/utils'
import { useUserStore } from '@/store'
import { size } from 'lodash-es'

defineOptions({ name: '接口测试用例' })

const $message = useMessage()
const $dialog = useDialog()
const $table = ref(null)
const userStore = useUserStore()

// 查询条件
const queryItems = ref({
  case_name: null,
  method: null,
  module_id: null,
  module_ids: null,
  status: null,
  is_smoke: null,
  source: null,
  project_id: null
})

// 项目相关
const projectOption = ref([])
const selectedKeys = ref([])
const selectedProjectId = ref(null)
const selectedProjectName = ref('')
const moduleOptions = ref([])

// 搜索相关
const searchKeyword = ref('')
const expandedKeys = ref([])
const treeData = ref([])
const filteredTreeData = ref([])

// 批量操作相关
const selectedRowKeys = ref([])
const batchStatusModalVisible = ref(false)
const batchStatus = ref('pending')

// 环境相关状态
const environmentOptions = ref([])
const selectedEnvironment = ref(null)
const selectedEnvironmentName = ref('选择环境')

// 执行结果弹窗相关状态
const executionModalVisible = ref(false)
const executionLoading = ref(false)
const executionResult = ref(null)

// 断言相关状态
const assertionTypes = [
  { label: '状态码断言', value: 'status_code' },
  { label: '响应时间断言', value: 'response_time' },
  { label: '响应体JSON路径断言', value: 'json_path' },
  { label: '响应体内容断言', value: 'response_body' },
  { label: '响应头断言', value: 'response_header' }
]

const assertionOperators = {
  status_code: [
    { label: '等于', value: 'equals' },
    { label: '不等于', value: 'not_equals' },
    { label: '大于', value: 'greater_than' },
    { label: '小于', value: 'less_than' },
    { label: '在范围内', value: 'in_range' }
  ],
  response_time: [
    { label: '小于', value: 'less_than' },
    { label: '大于', value: 'greater_than' },
    { label: '在范围内', value: 'in_range' }
  ],
  json_path: [
    { label: '等于', value: 'equals' },
    { label: '不等于', value: 'not_equals' },
    { label: '包含', value: 'contains' },
    { label: '不包含', value: 'not_contains' },
    { label: '正则匹配', value: 'regex' },
    { label: '存在', value: 'exists' },
    { label: '不存在', value: 'not_exists' }
  ],
  response_body: [
    { label: '完全匹配', value: 'exact_match' },
    { label: '部分匹配', value: 'partial_match' },
    { label: '包含', value: 'contains' },
    { label: '不包含', value: 'not_contains' },
    { label: '正则匹配', value: 'regex' },
    { label: 'JSON Schema验证', value: 'json_schema' }
  ],
  response_header: [
    { label: '存在', value: 'exists' },
    { label: '不存在', value: 'not_exists' },
    { label: '等于', value: 'equals' },
    { label: '包含', value: 'contains' },
    { label: '正则匹配', value: 'regex' }
  ]
}

// 复制用例相关
const copyModalVisible = ref(false)
const copyForm = ref({
  id: null,
  case_name: '',
  project_id: null
})

let lastClickedProjectId = null

// 状态选项
const statusOptions = [
  { label: '待审核', value: 'pending' },
  { label: '已审核', value: 'approved' }
]

// 来源选项
const sourceOptions = [
  { label: '人工', value: 'manual' },
  { label: 'AI', value: 'ai' }
]

// 请求方法选项
const methodOptions = [
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'PUT', value: 'PUT' },
  { label: 'DELETE', value: 'DELETE' },
  { label: 'PATCH', value: 'PATCH' }
]

// CRUD配置
const {
  modalVisible,
  modalTitle,
  modalAction,
  modalLoading,
  handleSave: originalHandleSave,
  modalForm,
  modalFormRef,
  handleEdit: originalHandleEdit,
  handleDelete,
  handleAdd: originalHandleAdd,
} = useCRUD({
  name: '测试用例',
  initForm: {
    case_name: '',
    method: 'GET',
    url: '',
    params: '',
    body: '',
    assertions: [],
    is_smoke: false,
    status: 'pending',
    source: 'manual',
    project_id: '',
    module_id: null,
  },
  doCreate: apiTestCaseApi.createApiTestCase,
  doUpdate: apiTestCaseApi.updateApiTestCase,
  doDelete: apiTestCaseApi.deleteApiTestCase,
  refresh: () => $table.value?.handleSearch(),
})

// 重写保存函数
const handleSave = async () => {
  try {
    // 将断言数据转换为expected_result字段
    if (modalForm.value.assertions && modalForm.value.assertions.length > 0) {
      modalForm.value.expected_result = JSON.stringify(modalForm.value.assertions)
    } else {
      modalForm.value.expected_result = ''
    }

    await originalHandleSave()

    // 保存成功后刷新表格
    const projectId = selectedProjectId.value || modalForm.value.project_id
    if (projectId) {
      queryItems.value.project_id = projectId
      $table.value?.handleSearch()
    }
  } catch (error) {
    console.error('保存失败:', error)
    $table.value?.handleSearch()
  }
}

// 重写编辑函数
const handleEdit = async (row) => {
  const editRow = { ...row }
  if (editRow.project_id !== undefined && editRow.project_id !== null) {
    editRow.project_id = String(editRow.project_id)
    await loadModuleOptions(editRow.project_id)
  }

  // 处理params字段，确保正确显示
  if (editRow.params) {
    if (typeof editRow.params === 'object') {
      editRow.params = JSON.stringify(editRow.params, null, 2)
    } else if (typeof editRow.params === 'string') {
      try {
        // 尝试格式化JSON字符串
        const parsed = JSON.parse(editRow.params)
        editRow.params = JSON.stringify(parsed, null, 2)
      } catch (e) {
        // 如果不是有效JSON，保持原样
      }
    }
  }

  // 处理断言数据
  if (editRow.expected_result) {
    try {
      // 尝试解析旧的expected_result为断言格式
      editRow.assertions = JSON.parse(editRow.expected_result)
    } catch (e) {
      // 如果不是JSON格式，创建一个默认的响应体断言
      editRow.assertions = [{
        id: Date.now(),
        type: 'response_body',
        operator: 'contains',
        expected_value: editRow.expected_result,
        description: '从预期结果转换'
      }]
    }
  } else {
    editRow.assertions = []
  }

  originalHandleEdit(editRow)
}

// 重写新增函数
const handleAdd = async () => {
  originalHandleAdd()

  if (selectedProjectId.value) {
    modalForm.value.project_id = String(selectedProjectId.value)
    await loadModuleOptions(selectedProjectId.value)
  }

  // 初始化断言数组
  modalForm.value.assertions = []
}

// 复制用例
const handleCopy = (row) => {
  copyForm.value = {
    id: row.id,
    case_name: `${row.case_name}_copy`,
    project_id: selectedProjectId.value || row.project_id
  }
  copyModalVisible.value = true
}

// 执行复制
const executeCopy = async () => {
  try {
    await apiTestCaseApi.copyApiTestCase(copyForm.value)
    $message.success('用例复制成功')
    copyModalVisible.value = false
    $table.value?.handleSearch()
  } catch (error) {
    $message.error('复制失败: ' + error.message)
  }
}

// 执行测试用例
const handleExecuteTestCase = async (row) => {
  if (!selectedEnvironment.value) {
    $message.error('请先选择执行环境')
    return
  }

  try {
    executionLoading.value = true
    executionModalVisible.value = true
    executionResult.value = null

    const response = await apiTestCaseApi.executeApiTestCase({
      test_case_id: row.id,
      environment_id: selectedEnvironment.value
    })

    if (response.code === 200) {
      executionResult.value = response.data
      // 刷新表格以更新执行次数和最后执行时间
      // $table.value?.handleSearch()
    } else {
      $message.error(response.msg || '测试用例执行失败')
    }
  } catch (error) {
    console.error('执行测试用例失败:', error)
    $message.error('执行测试用例失败: ' + error.message)
  } finally {
    executionLoading.value = false
  }
}

// 关闭执行结果弹窗
const handleCloseExecutionModal = () => {
  executionModalVisible.value = false
  executionResult.value = null
}

// 获取状态码类型
const getStatusCodeType = (statusCode) => {
  if (!statusCode) return 'default'
  if (statusCode >= 200 && statusCode < 300) return 'success'
  if (statusCode >= 300 && statusCode < 400) return 'info'
  if (statusCode >= 400 && statusCode < 500) return 'warning'
  if (statusCode >= 500) return 'error'
  return 'default'
}

// 复制响应体内容
const handleCopyResponse = async (content) => {
  try {
    const textToCopy = typeof content === 'object' ? JSON.stringify(content, null, 2) : content
    await navigator.clipboard.writeText(textToCopy)
    $message.success('复制成功')
  } catch (err) {
    $message.error('复制失败')
    console.error('复制失败:', err)
  }
}

// 格式化JSON字符串
const formatJson = (jsonString) => {
  try {
    if (typeof jsonString === 'object') {
      return JSON.stringify(jsonString, null, 2)
    }
    const parsed = JSON.parse(jsonString)
    return JSON.stringify(parsed, null, 2)
  } catch (e) {
    return jsonString
  }
}

// 解析URL和查询参数
const parseUrlAndParams = (url, method) => {
  if (method !== 'GET' || !url) {
    return { cleanUrl: url, params: '' }
  }

  try {
    const urlObj = new URL(url.startsWith('http') ? url : `http://example.com${url}`)
    const cleanUrl = urlObj.pathname
    const searchParams = urlObj.searchParams

    if (searchParams.toString()) {
      const paramsObj = {}
      for (const [key, value] of searchParams) {
        // 尝试转换数字
        if (!isNaN(value) && value !== '') {
          paramsObj[key] = Number(value)
        } else {
          paramsObj[key] = value
        }
      }
      return {
        cleanUrl,
        params: JSON.stringify(paramsObj, null, 2)
      }
    }

    return { cleanUrl, params: '' }
  } catch (error) {
    console.error('URL解析失败:', error)
    return { cleanUrl: url, params: '' }
  }
}

// 监听URL和方法变化，自动解析参数
const handleUrlOrMethodChange = () => {
  if (modalForm.value.method === 'GET' && modalForm.value.url) {
    const { cleanUrl, params } = parseUrlAndParams(modalForm.value.url, modalForm.value.method)
    modalForm.value.url = cleanUrl
    if (params && !modalForm.value.params) {
      modalForm.value.params = params
    }
  }
}

// 断言管理方法
const addAssertion = () => {
  if (!modalForm.value.assertions) {
    modalForm.value.assertions = []
  }
  modalForm.value.assertions.push({
    id: Date.now(),
    type: 'status_code',
    operator: 'equals',
    expected_value: '',
    json_path: '',
    header_name: '',
    description: ''
  })
}

const removeAssertion = (index) => {
  modalForm.value.assertions.splice(index, 1)
}

const getOperatorOptions = (type) => {
  return assertionOperators[type] || []
}

const getAssertionTypeLabel = (type) => {
  const typeOption = assertionTypes.find(t => t.value === type)
  return typeOption ? typeOption.label : type
}

const getAssertionOperatorLabel = (type, operator) => {
  const operators = assertionOperators[type] || []
  const operatorOption = operators.find(o => o.value === operator)
  return operatorOption ? operatorOption.label : operator
}

// 加载项目列表
const loadProjectList = async () => {
  try {
    const response = await projectApi.getProjectList()
    projectOption.value = response.data || []

    // 构建项目和模块的树形数据
    await buildTreeData()

    // 默认选择第一个项目
    if (projectOption.value.length > 0 && !selectedProjectId.value) {
      const firstProject = projectOption.value[0]
      selectedProjectId.value = firstProject.id
      selectedProjectName.value = firstProject.name
      selectedKeys.value = [`project_${firstProject.id}`]
      queryItems.value.project_id = firstProject.id
      await loadModuleOptions(firstProject.id)
      await loadEnvironmentOptions(firstProject.id)
      restoreEnvironmentSelection(firstProject.id)
      // 等待树形数据构建完成后再展开第一个项目
      await nextTick()
      expandAllProjectModules(firstProject.id)
      $table.value?.handleSearch()
    }
  } catch (error) {
    console.error('加载项目列表失败:', error)
  }
}

// 加载模块选项
const loadModuleOptions = async (projectId) => {
  try {
    const response = await projectApi.getProjectModuleTree({ project_id: projectId })
    const modules = response.data || []

    // 扁平化模块树，保持层级关系，只有叶子节点可选
    const flattenModules = (modules, result = [], level = 0) => {
      modules.forEach(module => {
        const prefix = '　'.repeat(level) // 使用全角空格缩进
        const hasChildren = module.children && module.children.length > 0

        result.push({
          label: `${prefix}${module.name}`,
          value: module.id,
          disabled: hasChildren // 有子模块的节点禁用，只能选择叶子节点
        })

        if (hasChildren) {
          flattenModules(module.children, result, level + 1)
        }
      })
      return result
    }

    moduleOptions.value = flattenModules(modules)
  } catch (error) {
    console.error('加载模块选项失败:', error)
    moduleOptions.value = []
  }
}

// 加载环境选项
const loadEnvironmentOptions = async (projectId) => {
  try {
    const response = await environmentApi.getEnvironments({ project_id: projectId })
    environmentOptions.value = (response.data || []).map(env => ({
      label: env.name,
      key: env.id,
      env: env
    }))
    console.log('加载环境选项成功:', environmentOptions.value)
  } catch (error) {
    console.error('加载环境选项失败:', error)
    environmentOptions.value = []
  }
}

// 获取环境选择存储键
const getEnvironmentStorageKey = (projectId) => {
  const userId = userStore.userId
  return `testcase_env_selection_${userId}_${projectId}`
}

// 保存环境选择
const saveEnvironmentSelection = (projectId, environmentId, environmentName) => {
  const key = getEnvironmentStorageKey(projectId)
  lStorage.set(key, { environmentId, environmentName })
}

// 恢复环境选择
const restoreEnvironmentSelection = (projectId) => {
  const key = getEnvironmentStorageKey(projectId)
  const saved = lStorage.get(key)
  if (saved && saved.environmentId) {
    // 检查环境是否还存在
    const envExists = environmentOptions.value.find(env => env.key === saved.environmentId)
    if (envExists) {
      selectedEnvironment.value = saved.environmentId
      selectedEnvironmentName.value = saved.environmentName
    } else {
      // 环境不存在，清除保存的选择
      lStorage.remove(key)
      selectedEnvironment.value = null
      selectedEnvironmentName.value = '选择环境'
    }
  } else {
    selectedEnvironment.value = null
    selectedEnvironmentName.value = '选择环境'
  }
}

// 处理环境选择
const handleEnvironmentSelect = (environmentId) => {
  console.log('选择环境:', environmentId, environmentOptions.value)
  const env = environmentOptions.value.find(e => e.key === environmentId)
  if (env) {
    selectedEnvironment.value = environmentId
    selectedEnvironmentName.value = env.label
    // 保存选择
    saveEnvironmentSelection(selectedProjectId.value, environmentId, env.label)
    $message.success(`已选择环境: ${env.label}`)
    console.log('环境选择成功:', selectedEnvironment.value, selectedEnvironmentName.value)
  } else {
    console.error('未找到环境:', environmentId)
  }
}

// 树节点属性
const treeNodeProps = ({ option }) => {
  return {
    onClick: async () => {
      if (option.type === 'project') {
        // 点击项目节点 - 显示项目下所有测试用例
        selectedKeys.value = [option.key]
        selectedProjectId.value = option.projectId
        selectedProjectName.value = option.label
        queryItems.value.project_id = option.projectId
        queryItems.value.module_id = null // 清空模块筛选，显示项目下所有用例
        queryItems.value.module_ids = null // 清空多模块筛选

        // 展开当前选中项目的所有模块
        expandAllProjectModules(option.projectId)

        // 加载该项目的模块
        loadModuleOptions(option.projectId)
        // 加载环境选项并等待完成
        await loadEnvironmentOptions(option.projectId)
        // 恢复该项目的环境选择
        restoreEnvironmentSelection(option.projectId)
        // 刷新表格
        $table.value?.handleSearch()
      } else if (option.type === 'module') {
        // 点击模块节点 - 显示该模块及其所有子模块的测试用例
        selectedKeys.value = [option.key]
        selectedProjectId.value = option.projectId
        selectedProjectName.value = option.projectName
        queryItems.value.project_id = option.projectId

        // 获取该模块及其所有子模块的ID
        const moduleIds = getAllModuleIdsFromTree(option.moduleId, option)
        if (moduleIds.length === 1) {
          // 只有一个模块，使用单模块查询
          queryItems.value.module_id = option.moduleId
          queryItems.value.module_ids = null
        } else {
          // 有多个模块（包含子模块），使用多模块查询
          queryItems.value.module_id = null
          queryItems.value.module_ids = moduleIds.join(',')
        }

        // 加载该项目的模块
        loadModuleOptions(option.projectId)
        // 加载环境选项并等待完成
        await loadEnvironmentOptions(option.projectId)
        // 恢复该项目的环境选择
        restoreEnvironmentSelection(option.projectId)
        // 刷新表格
        $table.value?.handleSearch()
      }
    }
  }
}

// 处理项目选择
const handleProjectSelect = async (project) => {
  try {
    selectedProjectId.value = project.id
    selectedProjectName.value = project.name
    selectedKeys.value = [project.id]
    lastClickedProjectId = project.id

    // 加载模块选项
    await loadModuleOptions(project.id)

    // 加载环境选项
    await loadEnvironmentOptions(project.id)

    // 恢复该项目的环境选择
    restoreEnvironmentSelection(project.id)

    // 更新查询条件并刷新表格
    queryItems.value.project_id = project.id
    queryItems.value.module_id = null
    $table.value?.handleSearch()
  } catch (error) {
    console.error('选择项目失败:', error)
  }
}

// 表格列定义
const columns = [
  {
    type: 'selection',
    width: 50
  },
  {
    title: '用例编号',
    key: 'case_number',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '用例名称',
    key: 'case_name',
    width: 150,
    ellipsis: {
      tooltip: true
    },

  },
  {
    title: '请求方式',
    key: 'method',
    width: 80,
    
    render: (row) => {
      const methodColors = {
        GET: 'success',
        POST: 'warning',
        PUT: 'info',
        DELETE: 'error',
        PATCH: 'default'
      }
      return h(NTag, { type: methodColors[row.method] || 'default' }, { default: () => row.method })
    }
  },
  {
    title: 'URL',
    key: 'url',
    width: 150,
    ellipsis: {
      tooltip: true
    },
    render(row) {
      const handleCopyUrl = () => {
        navigator.clipboard.writeText(row.url).then(() => {
          $message.success('URL已复制到剪贴板')
        }).catch(() => {
          $message.error('复制失败')
        })
      }
      return h(
        'span',
        {
          style: 'cursor: pointer; color: #1890ff; text-decoration: underline;',
          onClick: handleCopyUrl
        },
        row.url
      )
    }
  },
  {
    title: '冒烟用例',
    key: 'is_smoke',
    width: 80,
    render: (row) => {
      return h(NTag, { type: row.is_smoke ? 'success' : 'default' }, {
        default: () => row.is_smoke ? '是' : '否'
      })
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 80,
    render: (row) => {
      const statusMap = {
        pending: { label: '待审核', type: 'warning' },
        approved: { label: '已审核', type: 'success' }
      }
      const status = statusMap[row.status] || { label: row.status, type: 'default' }

      return h(NPopover, {
        trigger: 'click',
        placement: 'bottom'
      }, {
        trigger: () => h(NTag, {
          type: status.type,
          style: 'cursor: pointer;'
        }, { default: () => status.label }),
        default: () => h('div', {
          style: 'padding: 8px;'
        }, [
          h('div', {
            style: 'margin-bottom: 8px; font-weight: 500; color: #666;'
          }, '选择状态:'),
          h(NSpace, {
            vertical: true,
            size: 'small'
          }, {
            default: () => Object.entries(statusMap).map(([value, config]) =>
              h(NTag, {
                type: config.type,
                style: 'cursor: pointer; width: 80px; text-align: center;',
                bordered: row.status === value,
                onClick: () => handleUpdateApiTestCaseStatus(row.id, value)
              }, {
                default: () => config.label
              })
            )
          })
        ])
      })
    }
  },
  {
    title: '来源',
    key: 'source',
    width: 60,
    render: (row) => {
      const sourceMap = {
        manual: { label: '人工', type: 'info' },
        ai: { label: 'AI', type: 'success' }
      }
      const source = sourceMap[row.source] || { label: row.source, type: 'default' }
      return h(NTag, { type: source.type }, { default: () => source.label })
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 175,
    render: (row) => formatDate(row.created_at)
  },
  {
    title: '创建用户',
    key: 'username',
    width: 100,
    ellipsis: {
      tooltip: true
    },
    render: (row) => row.username || '-'
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'center',
    fixed: 'right',
    hideInExcel: true,
    render: (row) => {
      return h('div', { style: 'display: flex; gap: 8px; justify-content: center;' }, [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            secondary: true,
            onClick: () => handleEdit(row)
          },
          { default: () => '编辑' }
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'info',
            secondary: true,
            onClick: () => handleCopy(row)
          },
          { default: () => '复制' }
        ),
        // h(s
        h(
          NButton,
          {
            size: 'small',
            type: 'success',
            onClick: () => handleExecuteTestCase(row)
          },
          { default: () => '执行' }
        )
      ])
    }
  }
]

// 自定义重置处理 - 保持当前项目ID
const handleReset = async () => {
  console.log('=== 接口测试用例重置函数开始 ===')
  console.log('重置前 selectedProjectId:', selectedProjectId.value)
  console.log('重置前 queryItems:', queryItems.value)

  // 确保有选中的项目ID
  if (!selectedProjectId.value) {
    console.warn('没有选中的项目ID，使用默认重置逻辑')
    // 如果没有选中项目，清空所有查询条件
    queryItems.value = {
      case_name: null,
      method: null,
      module_id: null,
      module_ids: null,
      status: null,
      is_smoke: null,
      source: null,
      project_id: null
    }
  } else {
    // 重置查询条件，但保持当前选中的项目ID
    queryItems.value = {
      case_name: null,
      method: null,
      module_id: null,
      module_ids: null,
      status: null,
      is_smoke: null,
      source: null,
      project_id: selectedProjectId.value
    }
  }

  console.log('重置后 queryItems:', queryItems.value)

  // 等待响应式更新完成
  await nextTick()

  // 刷新表格数据
  $table.value?.handleSearch()
  console.log('=== 接口测试用例重置函数结束 ===')
}

// 监听 queryItems 变化，确保 project_id 不会被意外清空
watch(queryItems, (newVal, oldVal) => {
  console.log('接口测试用例 queryItems 变化:', { oldVal, newVal })

  // 如果 project_id 被清空但我们有选中的项目，则恢复它
  if (!newVal.project_id && selectedProjectId.value) {
    console.log('检测到 project_id 被清空，恢复为:', selectedProjectId.value)
    newVal.project_id = selectedProjectId.value
  }
}, { deep: true })

// 更新接口测试用例状态
const handleUpdateApiTestCaseStatus = async (testCaseId, newStatus) => {
  try {
    // 获取当前测试用例的完整信息
    const currentData = $table.value?.tableData?.find(item => item.id === testCaseId)
    if (!currentData) {
      $message.error('未找到测试用例数据')
      return
    }

    // 构建更新数据
    const updateData = {
      id: testCaseId,
      case_name: currentData.case_name,
      method: currentData.method,
      url: currentData.url,
      params: currentData.params,
      body: currentData.body,
      expected_result: currentData.expected_result,
      variable_extracts: currentData.variable_extracts,
      is_smoke: currentData.is_smoke,
      status: newStatus,
      source: currentData.source,
      project_id: currentData.project_id,
      module_id: currentData.module_id
    }

    await apiTestCaseApi.updateApiTestCase(updateData)
    $message.success('状态更新成功')

    // 刷新表格
    $table.value?.handleSearch()
  } catch (error) {
    console.error('更新状态失败:', error)
    $message.error('状态更新失败: ' + (error.message || '未知错误'))
  }
}

// 构建项目和模块的树形数据
const buildTreeData = async () => {
  try {
    const tree = []

    for (const project of projectOption.value) {
      // 获取项目的模块树
      const moduleResponse = await projectApi.getProjectModuleTree({ project_id: project.id })
      const modules = moduleResponse.data || []

      // 递归构建模块节点
      const buildModuleNodes = (moduleList, projectId, projectName) => {
        return moduleList.map(module => {
          const moduleNode = {
            key: `module_${module.id}`,
            label: module.name,
            type: 'module',
            projectId: projectId,
            projectName: projectName,
            moduleId: module.id
          }

          // 只有当模块有子模块时才添加children属性
          if (module.children && module.children.length > 0) {
            moduleNode.children = buildModuleNodes(module.children, projectId, projectName)
          }

          return moduleNode
        })
      }

      // 构建项目节点
      const projectNode = {
        key: `project_${project.id}`,
        label: project.name,
        type: 'project',
        projectId: project.id
      }

      // 只有当项目有模块时才添加children属性
      if (modules && modules.length > 0) {
        projectNode.children = buildModuleNodes(modules, project.id, project.name)
      }

      tree.push(projectNode)
    }

    treeData.value = tree
    filteredTreeData.value = tree

    // 默认展开当前选中的项目及其所有模块
    if (selectedProjectId.value) {
      expandAllProjectModules(selectedProjectId.value)
    } else {
      expandedKeys.value = []
    }
  } catch (error) {
    console.error('构建树形数据失败:', error)
  }
}

// 展开指定项目的所有模块
const expandAllProjectModules = (projectId) => {
  const keysToExpand = [`project_${projectId}`]

  // 递归收集所有模块节点的key
  const collectModuleKeys = (nodes) => {
    nodes.forEach(node => {
      if (node.type === 'module') {
        keysToExpand.push(node.key)
      }
      if (node.children && node.children.length > 0) {
        collectModuleKeys(node.children)
      }
    })
  }

  // 找到对应的项目节点并收集其所有模块key
  const projectNode = treeData.value.find(node => node.projectId === projectId)
  if (projectNode && projectNode.children) {
    collectModuleKeys(projectNode.children)
  }

  expandedKeys.value = keysToExpand
}

// 从树形数据中获取模块及其所有子模块的ID列表
const getAllModuleIdsFromTree = (moduleId, moduleNode) => {
  const moduleIds = [moduleId]

  // 递归收集子模块ID
  const collectChildIds = (node) => {
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => {
        if (child.type === 'module') {
          moduleIds.push(child.moduleId)
          collectChildIds(child)
        }
      })
    }
  }

  // 从当前节点开始收集子模块
  collectChildIds(moduleNode)

  return moduleIds
}

// 搜索相关函数
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    filteredTreeData.value = treeData.value
    // 清空搜索时，展开当前选中项目的所有模块
    if (selectedProjectId.value) {
      expandAllProjectModules(selectedProjectId.value)
    } else {
      expandedKeys.value = []
    }
    return
  }

  const keyword = searchKeyword.value.toLowerCase()
  const filtered = []
  const newExpandedKeys = []

  // 递归过滤函数 - 改进版，确保所有匹配的节点都能显示
  const filterNode = (node) => {
    const nodeMatches = node.label.toLowerCase().includes(keyword)
    let hasMatchingChildren = false
    let filteredChildren = []

    if (node.children && node.children.length > 0) {
      filteredChildren = node.children.map(child => filterNode(child)).filter(Boolean)
      hasMatchingChildren = filteredChildren.length > 0
    }

    // 如果当前节点匹配或有匹配的子节点，则包含此节点
    if (nodeMatches || hasMatchingChildren) {
      const filteredNode = {
        ...node
      }

      // 只有当有过滤后的子节点时才添加children属性
      if (filteredChildren.length > 0) {
        filteredNode.children = filteredChildren
        // 展开包含匹配项的节点
        newExpandedKeys.push(node.key)
      }
      // 如果节点本身匹配但没有子节点，不添加children属性

      return filteredNode
    }

    return null
  }

  // 递归收集所有需要展开的节点
  const collectExpandedKeys = (node, keyword) => {
    const nodeMatches = node.label.toLowerCase().includes(keyword)

    if (nodeMatches) {
      newExpandedKeys.push(node.key)
    }

    if (node.children && node.children.length > 0) {
      node.children.forEach(child => {
        collectExpandedKeys(child, keyword)
      })
    }
  }

  treeData.value.forEach(node => {
    const filteredNode = filterNode(node)
    if (filteredNode) {
      filtered.push(filteredNode)
      // 收集所有需要展开的节点
      collectExpandedKeys(node, keyword)
    }
  })

  filteredTreeData.value = filtered
  expandedKeys.value = [...new Set(newExpandedKeys)] // 去重
}

const handleClearSearch = () => {
  searchKeyword.value = ''
  filteredTreeData.value = treeData.value
  // 清空搜索时，展开当前选中项目的所有模块
  if (selectedProjectId.value) {
    expandAllProjectModules(selectedProjectId.value)
  } else {
    expandedKeys.value = []
  }
}

const handleExpandedKeysChange = (keys) => {
  expandedKeys.value = keys
}

// 高亮搜索关键词
const highlightSearchKeyword = (text) => {
  if (!searchKeyword.value || !searchKeyword.value.trim()) {
    return text
  }

  const keyword = searchKeyword.value.trim()
  // 转义特殊字符，防止正则表达式错误
  const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  const regex = new RegExp(`(${escapedKeyword})`, 'gi')
  return text.replace(regex, '<span style="background-color: #ffeb3b; color: #000; font-weight: bold; padding: 1px 2px; border-radius: 2px;">$1</span>')
}

// 批量操作相关方法
const handleTableChecked = (rowKeys) => {
  selectedRowKeys.value = rowKeys
}

const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    $message.warning('请先选择要删除的测试用例')
    return
  }

  $dialog.warning({
    title: '确认删除',
    content: `确定要删除选中的测试用例吗？删除后将自动重置编号，此操作不可恢复。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await apiTestCaseApi.batchDeleteApiTestCases({ ids: selectedRowKeys.value })
        $message.success(`成功删除测试用例`)

        // 清除选中状态
        selectedRowKeys.value = []

        // 刷新表格数据
        await $table.value?.handleSearch()
      } catch (error) {
        $message.error('批量删除失败: ' + error.message)
      }
    }
  })
}

const handleBatchUpdateStatus = () => {
  if (selectedRowKeys.value.length === 0) {
    $message.warning('请先选择要修改状态的测试用例')
    return
  }
  batchStatusModalVisible.value = true
}

const executeBatchUpdateStatus = async () => {
  try {
    await apiTestCaseApi.batchUpdateApiTestCaseStatus({
      ids: selectedRowKeys.value,
      status: batchStatus.value
    })

    $message.success(`成功修改测试用例的状态`)

    // 清除选中状态
    selectedRowKeys.value = []
    batchStatusModalVisible.value = false

    // 刷新表格数据
    await $table.value?.handleSearch()

    console.log('批量状态更新完成，selectedRowKeys.value:', selectedRowKeys.value)
  } catch (error) {
    $message.error('批量修改状态失败: ' + error.message)
  }
}

// 生命周期
onMounted(async () => {
  await loadProjectList()
})
</script>

<template>
  <NLayout has-sider wh-full>
    <NLayoutSider
      bordered
      content-style="padding: 24px;"
      :collapsed-width="0"
      :width="280"
      show-trigger="arrow-circle"
    >
      <h1>项目列表</h1>
      <br />
      <!-- 搜索框 -->
      <div style="margin-bottom: 16px;">
        <NInput
          v-model:value="searchKeyword"
          placeholder="搜索项目或模块..."
          clearable
          @input="handleSearch"
          @clear="handleClearSearch"
        >
          <template #prefix>
            <TheIcon icon="material-symbols:search" :size="16" />
          </template>
        </NInput>
      </div>

      <!-- 项目模块树 -->
      <NTree
        block-line
        :data="filteredTreeData"
        key-field="key"
        label-field="label"
        :expanded-keys="expandedKeys"
        :node-props="treeNodeProps"
        :selected-keys="selectedKeys"
        @update:expanded-keys="handleExpandedKeysChange"
      >
        <template #prefix="{ option }">
          <TheIcon
            :icon="option.type === 'project' ? 'material-symbols:folder' : 'material-symbols:topic'"
            :size="16"
            :style="{ color: option.type === 'project' ? '#1890ff' : '#52c41a' }"
          />
        </template>
        <template #default="{ option }">
          <span v-html="highlightSearchKeyword(option.label)"></span>
        </template>
      </NTree>
    </NLayoutSider>
    <NLayoutContent>
      <CommonPage show-footer :title="selectedProjectName ? `${selectedProjectName} - 接口测试用例` : '接口测试用例'">
        <template #action>
          <div style="display: flex; gap: 12px; align-items: center;">
            <!-- 环境选择按钮 -->
            <NDropdown
              v-if="selectedProjectId && environmentOptions.length > 0"
              :options="environmentOptions"
              @select="handleEnvironmentSelect"
              trigger="click"
            >
              <NButton type="default">
                <TheIcon icon="material-symbols:settings" :size="18" class="mr-5" />
                {{ selectedEnvironmentName }}
                <TheIcon icon="material-symbols:arrow-drop-down" :size="18" class="ml-5" />
              </NButton>
            </NDropdown>

            <!-- 新建测试用例按钮 -->
            <NButton
              v-if="selectedProjectId"
              type="primary"
              @click="handleAdd"
            >
              <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />新建
            </NButton>
          </div>
        </template>
        <!-- 表格 -->
        <CrudTable
          ref="$table"
          v-model:query-items="queryItems"
          v-model:checked-row-keys="selectedRowKeys"
          :columns="columns"
          :get-data="apiTestCaseApi.getApiTestCaseList"
          @reset="handleReset"
          @on-checked="handleTableChecked"
        >
          <template #queryBar>
            <QueryBarItem label="" :label-width="80">
              <NInput
                v-model:value="queryItems.case_name"
                clearable
                type="text"
                placeholder="请输入用例名称"
                @keypress.enter="$table?.handleSearch()"
              />
            </QueryBarItem>
<!--            <QueryBarItem label="" :label-width="80">-->
<!--              <NSelect-->
<!--                v-model:value="queryItems.method"-->
<!--                clearable-->
<!--                :options="methodOptions"-->
<!--                placeholder="请求方式"-->
<!--                style="width: 100px"-->
<!--                @update:value="$table?.handleSearch()"-->
<!--              />-->
<!--            </QueryBarItem>-->
<!--            <QueryBarItem label="" :label-width="80">-->
<!--              <NSelect-->
<!--                v-model:value="queryItems.module_id"-->
<!--                clearable-->
<!--                :options="moduleOptions"-->
<!--                placeholder="模块"-->
<!--                style="width: 100px"-->
<!--                @update:value="$table?.handleSearch()"-->
<!--              />-->
<!--            </QueryBarItem>-->
            <QueryBarItem label="" :label-width="50">
              <NSelect
                v-model:value="queryItems.status"
                clearable
                :options="statusOptions"
                placeholder="状态"
                style="width: 100px"
                @update:value="$table?.handleSearch()"
              />
            </QueryBarItem>
            <QueryBarItem label="" :label-width="50">
              <NSelect
                v-model:value="queryItems.source"
                clearable
                :options="sourceOptions"
                placeholder="来源"
                style="width: 100px"
                @update:value="$table?.handleSearch()"
              />
            </QueryBarItem>
            <QueryBarItem label="" :label-width="80">
              <NSelect
                v-model:value="queryItems.is_smoke"
                clearable
                :options="[
                  { label: '是', value: true },
                  { label: '否', value: false }
                ]"
                placeholder="冒烟"
                style="width: 100px"
                @update:value="$table?.handleSearch()"
              />
            </QueryBarItem>
          </template>
          <template #actions>
            <!-- 批量操作按钮 -->
            <NButton
              type="error"
              :disabled="selectedRowKeys.length === 0"
              @click="handleBatchDelete"
              :style="{ marginRight: '20px' }"
            >
              删除
            </NButton>

            <NButton
              type="warning"
              :disabled="selectedRowKeys.length === 0"
              @click="handleBatchUpdateStatus"
            >
              状态
            </NButton>
          </template>
        </CrudTable>
      </CommonPage>
    </NLayoutContent>
  </NLayout>

  <!-- 新增/编辑 弹窗 -->
  <CrudModal
    v-model:visible="modalVisible"
    :title="modalTitle"
    :loading="modalLoading"
    @save="handleSave"
    width="800px"
  >
    <NForm
      ref="modalFormRef"
      label-placement="left"
      label-align="left"
      :label-width="100"
      :model="modalForm"
    >
      <NFormItem label="用例名称" path="case_name">
        <NInput v-model:value="modalForm.case_name" clearable placeholder="请输入用例名称" />
      </NFormItem>
      <NFormItem label="请求方式" path="method">
        <NSelect
          v-model:value="modalForm.method"
          :options="methodOptions"
          placeholder="请选择请求方式"
          @update:value="handleUrlOrMethodChange"
        />
      </NFormItem>
      <NFormItem label="请求URL" path="url">
        <NInput
          v-model:value="modalForm.url"
          clearable
          placeholder="请输入请求URL"
          @blur="handleUrlOrMethodChange"
        />
      </NFormItem>
      <NFormItem label="所属项目" path="project_id">
        <NSelect
          v-model:value="modalForm.project_id"
          :options="projectOption.map(p => ({ label: p.name, value: String(p.id) }))"
          placeholder="请选择项目"
          @update:value="(value) => loadModuleOptions(value)"
        />
      </NFormItem>
      <NFormItem label="所属模块" path="module_id">
        <NSelect
          v-model:value="modalForm.module_id"
          clearable
          :options="moduleOptions"
          placeholder="请选择模块"
        />
      </NFormItem>
      <NFormItem label="请求参数" path="params">
        <template #label>
          <div style="display: flex; align-items: center; gap: 8px;">
            <span>请求参数</span>
            <NTooltip>
              <template #trigger>
                <component :is="renderIcon('mdi:information', { size: 16, color: '#909399' })" />
              </template>
              支持Faker变量，如：${name}、${email}、${phone}等。<br/>
              点击菜单"Faker数据生成器"查看所有可用变量
            </NTooltip>
          </div>
        </template>
        <NInput
          v-model:value="modalForm.params"
          type="textarea"
          :rows="3"
          placeholder="请输入请求参数（JSON格式），支持Faker变量如：${name}、${email}"
        />
      </NFormItem>
      <NFormItem label="请求体" path="body">
        <template #label>
          <div style="display: flex; align-items: center; gap: 8px;">
            <span>请求体</span>
            <NTooltip>
              <template #trigger>
                <component :is="renderIcon('mdi:information', { size: 16, color: '#909399' })" />
              </template>
              支持Faker变量，如：${name}、${email}、${phone}等。<br/>
              点击菜单"Faker数据生成器"查看所有可用变量
            </NTooltip>
          </div>
        </template>
        <NInput
          v-model:value="modalForm.body"
          type="textarea"
          :rows="4"
          placeholder="请输入请求体内容，支持Faker变量如：${name}、${email}"
        />
      </NFormItem>
      <NFormItem label="断言配置" path="assertions" style="width: 100%;">
        <div style="border: 1px solid #e0e0e0; border-radius: 4px; padding: 8px; width: 100%;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
            <span style="font-weight: 500;">断言规则</span>
            <NButton size="small" type="primary" @click="addAssertion">
              <TheIcon icon="material-symbols:add" :size="16" class="mr-5" />添加断言
            </NButton>
          </div>

          <div v-if="!modalForm.assertions || modalForm.assertions.length === 0" style="text-align: center; color: #999; padding: 20px;">
            暂无断言规则，点击"添加断言"开始配置
          </div>

          <div v-for="(assertion, index) in modalForm.assertions" :key="assertion.id" style="border: 1px solid #f0f0f0; border-radius: 4px; padding: 12px; margin-bottom: 12px; background: #fafafa;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
              <span style="font-weight: 500; color: #666;">断言 {{ index + 1 }}</span>
              <NButton size="tiny" type="error" @click="removeAssertion(index)">
                <TheIcon icon="material-symbols:delete" :size="14" />
              </NButton>
            </div>

            <!-- 断言类型、操作符和期望值在一行 -->
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 12px; margin-bottom: 4px;">
              <NFormItem label="断言类型" :show-label="false">
                <NSelect
                  v-model:value="assertion.type"
                  :options="assertionTypes"
                  placeholder="选择断言类型"
                  size="small"
                />
              </NFormItem>
              <NFormItem label="操作符" :show-label="false">
                <NSelect
                  v-model:value="assertion.operator"
                  :options="getOperatorOptions(assertion.type)"
                  placeholder="选择操作符"
                  size="small"
                />
              </NFormItem>
              <!-- 期望值字段（非JSON Schema） -->
              <NFormItem v-if="!['exists', 'not_exists', 'json_schema'].includes(assertion.operator)" label="期望值" :show-label="false">
                <NInput
                  v-model:value="assertion.expected_value"
                  placeholder="请输入期望值"
                  size="small"
                />
              </NFormItem>
            </div>

            <!-- JSON路径字段 -->
            <div v-if="assertion.type === 'json_path'" style="margin-bottom: 2px;">
              <NFormItem label="JSON路径" :show-label="false">
                <NInput
                  v-model:value="assertion.json_path"
                  placeholder="例如: $.data.id 或 $.users[0].name"
                  size="small"
                />
              </NFormItem>
            </div>

            <!-- 响应头名称字段 -->
            <div v-if="assertion.type === 'response_header'" style="margin-bottom: 2px;">
              <NFormItem label="响应头名称" :show-label="false">
                <NInput
                  v-model:value="assertion.header_name"
                  placeholder="例如: Content-Type"
                  size="small"
                />
              </NFormItem>
            </div>

            <!-- JSON Schema期望值单独一行 -->
            <div v-if="assertion.operator === 'json_schema'" style="margin-bottom: 2px;">
              <NFormItem label="JSON Schema" :show-label="false">
                <NInput
                  v-model:value="assertion.expected_value"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入JSON Schema"
                  size="small"
                />
              </NFormItem>
            </div>
            <!-- 描述字段 -->
            <NFormItem label="描述" :show-label="false" style="margin-bottom: 0; margin-top: -4px;">
              <NInput
                v-model:value="assertion.description"
                placeholder="断言描述（可选）"
                size="small"
              />
            </NFormItem>
          </div>
        </div>
      </NFormItem>
      <NFormItem label="是否冒烟用例" path="is_smoke">
        <NSwitch v-model:value="modalForm.is_smoke" />
      </NFormItem>
      <NFormItem label="状态" path="status">
        <NSelect
          v-model:value="modalForm.status"
          :options="statusOptions"
          placeholder="请选择状态"
        />
      </NFormItem>
      <NFormItem label="来源" path="source">
        <NSelect
          v-model:value="modalForm.source"
          :options="sourceOptions"
          placeholder="请选择来源"
        />
      </NFormItem>
    </NForm>
  </CrudModal>

  <!-- 复制用例弹窗 -->
  <CrudModal
    v-model:visible="copyModalVisible"
    title="复制测试用例"
    @save="executeCopy"
  >
    <NForm
      label-placement="left"
      label-align="left"
      :label-width="100"
      :model="copyForm"
    >
      <NFormItem label="新用例名称" path="case_name">
        <NInput v-model:value="copyForm.case_name" clearable placeholder="请输入新用例名称" />
      </NFormItem>
      <NFormItem label="目标项目" path="project_id">
        <NSelect
          v-model:value="copyForm.project_id"
          :options="projectOption.map(p => ({ label: p.name, value: p.id }))"
          placeholder="请选择目标项目"
        />
      </NFormItem>
    </NForm>
  </CrudModal>

  <!-- 执行结果弹窗 -->
  <NModal
    v-model:show="executionModalVisible"
    preset="card"
    title="测试用例执行结果"
    style="width: 90%; max-width: 1200px;"
    :mask-closable="true"
    :closable="true"
    :auto-focus="false"
  >
    <div style="height: 70vh; overflow: hidden; display: flex; flex-direction: column;">
      <NSpin :show="executionLoading" style="flex: 1;">
        <div v-if="executionResult" style="height: 100%; display: flex; flex-direction: column;">
          <!-- 基本信息 -->
          <div style="margin-bottom: 16px; flex-shrink: 0;">
            <NTag
              :type="executionResult.success ? 'success' : 'error'"
              style="margin-right: 8px;"
              size="medium"
            >
              {{ executionResult.success ? '✓ 执行成功' : '✗ 执行失败' }}
            </NTag>
            <NTag
              :type="getStatusCodeType(executionResult.status_code)"
              style="margin-right: 8px;"
              size="medium"
            >
              状态码: {{ executionResult.status_code || '无' }}
            </NTag>
            <NTag type="info" size="medium">
              执行时间: {{ executionResult.execution_time }}ms
            </NTag>
            <NTag
              v-if="executionResult.assertion_results"
              :type="executionResult.assertion_results.all_passed ? 'success' : 'error'"
              style="margin-left: 8px;"
              size="medium"
            >
              断言结果: {{ executionResult.assertion_results.all_passed ? '全部通过' : '存在失败' }}
              ({{ executionResult.assertion_results.passed_count }}/{{ executionResult.assertion_results.total_count }})
            </NTag>
          </div>

          <!-- 错误信息 -->
          <div v-if="!executionResult.success && executionResult.error_message"
               style="margin-bottom: 16px; flex-shrink: 0;">
            <NCard title="错误信息" size="small" style="border: 1px solid #f56565;">
              <div style="max-height: 150px; overflow-y: auto; background: #fef5e7; padding: 12px; border-radius: 4px;">
                <pre style="margin: 0; white-space: pre-wrap; word-break: break-word; font-family: 'Courier New', monospace; font-size: 13px;">{{ executionResult.error_message }}</pre>
              </div>
            </NCard>
          </div>

          <!-- 响应内容 -->
          <div v-if="executionResult.success" style="flex: 1; min-height: 0;">
            <NTabs type="line" animated style="height: 100%;">
              <NTabPane name="body" tab="响应体">
                <div style="height: 50vh; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 4px; background: #f8f9fa; position: relative;">
                  <NButton
                    quaternary
                    circle
                    size="small"
                    style="position: absolute; top: 8px; right: 8px; z-index: 1;"
                    @click="handleCopyResponse(executionResult.response_body)"
                  >
                    <TheIcon icon="material-symbols:content-copy" :size="16" />
                  </NButton>
                  <pre style="margin: 0; padding: 16px; white-space: pre-wrap; word-break: break-word; font-family: 'Courier New', monospace; font-size: 13px; line-height: 1.5;">{{ formatJson(executionResult.response_body || '') }}</pre>
                </div>
              </NTabPane>
              <NTabPane name="headers" tab="响应头">
                <div style="height: 50vh; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 4px; background: #f8f9fa; position: relative;">
                  <NButton
                    quaternary
                    circle
                    size="small"
                    style="position: absolute; top: 8px; right: 8px; z-index: 1;"
                    @click="handleCopyResponse(executionResult.response_headers)"
                  >
                    <TheIcon icon="material-symbols:content-copy" :size="16" />
                  </NButton>
                  <pre style="margin: 0; padding: 16px; white-space: pre-wrap; word-break: break-word; font-family: 'Courier New', monospace; font-size: 13px; line-height: 1.5;">{{ JSON.stringify(executionResult.response_headers || {}, null, 2) }}</pre>
                </div>
              </NTabPane>
              <NTabPane name="request" tab="请求信息">
                <div style="height: 50vh; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 4px; background: #f8f9fa; position: relative;">
                  <NButton
                    quaternary
                    circle
                    size="small"
                    style="position: absolute; top: 8px; right: 8px; z-index: 1;"
                    @click="handleCopyResponse(executionResult.request_info)"
                  >
                    <TheIcon icon="material-symbols:content-copy" :size="16" />
                  </NButton>
                  <pre style="margin: 0; padding: 16px; white-space: pre-wrap; word-break: break-word; font-family: 'Courier New', monospace; font-size: 13px; line-height: 1.5;">{{ JSON.stringify(executionResult.request_info || {}, null, 2) }}</pre>
                </div>
              </NTabPane>
              <NTabPane v-if="executionResult.assertion_results" name="assertions" tab="断言详情">
                <div style="height: 50vh; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 4px; background: #f8f9fa; padding: 16px;">
                  <div v-if="executionResult.assertion_results.results.length === 0" style="text-align: center; color: #999; padding: 40px;">
                    该测试用例未配置断言
                  </div>
                  <div v-else>
                    <div v-for="(result, index) in executionResult.assertion_results.results" :key="index"
                         style="border: 1px solid #e0e0e0; border-radius: 4px; padding: 12px; margin-bottom: 12px; background: white;">
                      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <span style="font-weight: 500;">断言 {{ index + 1 }}</span>
                        <NTag :type="result.passed ? 'success' : 'error'" size="small">
                          {{ result.passed ? '通过' : '失败' }}
                        </NTag>
                      </div>

                      <div style="margin-bottom: 8px;">
                        <span style="color: #666; font-size: 12px;">类型:</span>
                        <span style="margin-left: 8px;">{{ getAssertionTypeLabel(result.type) }}</span>
                      </div>

                      <div style="margin-bottom: 8px;">
                        <span style="color: #666; font-size: 12px;">操作符:</span>
                        <span style="margin-left: 8px;">{{ getAssertionOperatorLabel(result.type, result.operator) }}</span>
                      </div>

                      <div v-if="result.json_path" style="margin-bottom: 8px;">
                        <span style="color: #666; font-size: 12px;">JSON路径:</span>
                        <span style="margin-left: 8px; font-family: monospace;">{{ result.json_path }}</span>
                      </div>

                      <div v-if="result.header_name" style="margin-bottom: 8px;">
                        <span style="color: #666; font-size: 12px;">响应头:</span>
                        <span style="margin-left: 8px; font-family: monospace;">{{ result.header_name }}</span>
                      </div>

                      <div v-if="result.expected_value !== undefined && result.expected_value !== ''" style="margin-bottom: 8px;">
                        <span style="color: #666; font-size: 12px;">期望值:</span>
                        <span style="margin-left: 8px; font-family: monospace;">{{ result.expected_value }}</span>
                      </div>

                      <div v-if="result.actual_value !== undefined" style="margin-bottom: 8px;">
                        <span style="color: #666; font-size: 12px;">实际值:</span>
                        <span style="margin-left: 8px; font-family: monospace;">{{ result.actual_value }}</span>
                      </div>

                      <div v-if="result.description" style="margin-bottom: 8px;">
                        <span style="color: #666; font-size: 12px;">描述:</span>
                        <span style="margin-left: 8px;">{{ result.description }}</span>
                      </div>

                      <div v-if="!result.passed && result.error_message" style="margin-top: 8px; padding: 8px; background: #fef2f2; border-radius: 4px; border-left: 3px solid #ef4444;">
                        <span style="color: #dc2626; font-size: 12px; font-weight: 500;">错误信息:</span>
                        <div style="color: #dc2626; font-size: 12px; margin-top: 4px;">{{ result.error_message }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </NTabPane>
            </NTabs>
          </div>
        </div>
      </NSpin>
    </div>

    <template #footer>
      <div style="text-align: right;">
        <NButton @click="handleCloseExecutionModal" type="primary">关闭</NButton>
      </div>
    </template>
  </NModal>

  <!-- 批量修改状态弹窗 -->
  <CrudModal
    v-model:visible="batchStatusModalVisible"
    title="批量修改状态"
    @save="executeBatchUpdateStatus"
  >
    <NForm
      label-placement="left"
      label-align="left"
      :label-width="100"
    >
      <NFormItem label="选择状态">
        <NSelect
          v-model:value="batchStatus"
          :options="statusOptions"
          placeholder="请选择状态"
        />
      </NFormItem>
      <NFormItem>
        <NText depth="3">
          将为选中的测试用例修改状态
        </NText>
      </NFormItem>
    </NForm>
  </CrudModal>
</template>

<style scoped>
/* 搜索高亮样式 */
:deep(.n-tree-node-content) {
  overflow: visible !important;
}

:deep(.n-tree-node-content__text) {
  overflow: visible !important;
  white-space: nowrap;
}

/* 树节点悬停效果 */
:deep(.n-tree-node:hover .n-tree-node-content) {
  background-color: rgba(24, 144, 255, 0.1);
}

/* 选中节点样式 */
:deep(.n-tree-node--selected .n-tree-node-content) {
  background-color: rgba(24, 144, 255, 0.2);
  font-weight: 500;
}
</style>