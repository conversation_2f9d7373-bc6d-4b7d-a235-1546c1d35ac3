import asyncio
import time
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date, time as time_type
from decimal import Decimal
import aiomysql
# import asyncpg
import aiosqlite
from tortoise.queryset import Q

from app.core.crud import CRUDBase
from app.models.admin import DatabaseConnection, QueryHistory
from app.schemas.database_query import (
    DatabaseConnectionCreate, 
    DatabaseConnectionUpdate,
    QueryExecuteRequest,
    QueryResult,
    TableInfo,
    ColumnInfo,
    DatabaseSchema,
    TableSchema,
    ConnectionTestResult
)
from app.core.ctx import CTX_USER_ID
import logging

logger = logging.getLogger(__name__)


def serialize_value(value: Any) -> Any:
    """
    将数据库查询结果中的特殊类型转换为JSON可序列化的类型
    """
    if value is None:
        return None
    elif isinstance(value, (datetime, date)):
        return value.isoformat()
    elif isinstance(value, time_type):
        return value.isoformat()
    elif isinstance(value, Decimal):
        return float(value)
    elif isinstance(value, bytes):
        # 对于二进制数据，转换为base64字符串或十六进制字符串
        try:
            return value.decode('utf-8')
        except UnicodeDecodeError:
            return value.hex()
    else:
        return value


def serialize_row_data(data: List[List[Any]]) -> List[List[Any]]:
    """
    序列化查询结果数据，确保所有值都可以JSON序列化
    """
    return [[serialize_value(value) for value in row] for row in data]


class DatabaseQueryController(CRUDBase[DatabaseConnection, DatabaseConnectionCreate, DatabaseConnectionUpdate]):
    def __init__(self):
        super().__init__(model=DatabaseConnection)

    async def create_connection(self, obj_in: DatabaseConnectionCreate, user_id: int) -> DatabaseConnection:
        """创建数据库连接配置"""
        obj_dict = obj_in.model_dump()
        obj_dict["user_id"] = user_id
        return await self.model.create(**obj_dict)

    async def update_connection(self, connection_id: int, obj_in: DatabaseConnectionUpdate, user_id: int) -> DatabaseConnection:
        """更新数据库连接配置"""
        connection = await self.model.get(id=connection_id, user_id=user_id)
        update_data = obj_in.model_dump(exclude_unset=True)
        await connection.update_from_dict(update_data).save()
        return connection

    async def delete_connection(self, connection_id: int, user_id: int) -> bool:
        """删除数据库连接配置"""
        connection = await self.model.get(id=connection_id, user_id=user_id)
        await connection.delete()
        return True

    async def get_user_connections(self, user_id: int) -> List[DatabaseConnection]:
        """获取用户的数据库连接列表"""
        return await self.model.filter(user_id=user_id, is_active=True).order_by("-created_at")

    async def test_connection(self, connection_id: int, user_id: int) -> ConnectionTestResult:
        """测试数据库连接"""
        connection = await self.model.get(id=connection_id, user_id=user_id)
        start_time = time.time()
        
        try:
            if connection.db_type.lower() == "mysql":
                conn = await aiomysql.connect(
                    host=connection.host,
                    port=connection.port,
                    user=connection.username,
                    password=connection.password,
                    db=connection.database,
                    charset=connection.charset,
                    connect_timeout=connection.connection_timeout
                )
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT VERSION()")
                    result = await cursor.fetchone()
                    server_version = result[0] if result else None
                await conn.ensure_closed()
                
            # elif connection.db_type.lower() == "postgresql":
            #     conn = await asyncpg.connect(
            #         host=connection.host,
            #         port=connection.port,
            #         user=connection.username,
            #         password=connection.password,
            #         database=connection.database,
            #         timeout=connection.connection_timeout
            #     )
            #     server_version = await conn.fetchval("SELECT version()")
            #     await conn.close()
                
            elif connection.db_type.lower() == "sqlite":
                async with aiosqlite.connect(connection.database) as conn:
                    cursor = await conn.execute("SELECT sqlite_version()")
                    result = await cursor.fetchone()
                    server_version = result[0] if result else None
            else:
                return ConnectionTestResult(
                    success=False,
                    message=f"不支持的数据库类型: {connection.db_type}",
                    connection_time=int((time.time() - start_time) * 1000)
                )
            
            connection_time = int((time.time() - start_time) * 1000)
            return ConnectionTestResult(
                success=True,
                message="连接成功",
                server_version=server_version,
                connection_time=connection_time
            )
            
        except Exception as e:
            connection_time = int((time.time() - start_time) * 1000)
            logger.error(f"数据库连接测试失败: {str(e)}")
            return ConnectionTestResult(
                success=False,
                message=f"连接失败: {str(e)}",
                connection_time=connection_time
            )

    async def execute_query(self, request: QueryExecuteRequest, user_id: int) -> QueryResult:
        """执行SQL查询"""
        connection = await self.model.get(id=request.database_connection_id, user_id=user_id)
        start_time = time.time()
        
        try:
            if connection.db_type.lower() == "mysql":
                result = await self._execute_mysql_query(connection, request)
            elif connection.db_type.lower() == "postgresql":
                result = await self._execute_postgresql_query(connection, request)
            elif connection.db_type.lower() == "sqlite":
                result = await self._execute_sqlite_query(connection, request)
            else:
                raise ValueError(f"不支持的数据库类型: {connection.db_type}")
            
            execution_time = int((time.time() - start_time) * 1000)
            result.execution_time = execution_time
            
            # 保存查询历史
            await self._save_query_history(
                request, user_id, execution_time, 
                result.total_count, result.affected_rows, "success", None
            )
            
            return result
            
        except Exception as e:
            execution_time = int((time.time() - start_time) * 1000)
            error_msg = str(e)
            logger.error(f"SQL查询执行失败: {error_msg}")
            
            # 保存失败的查询历史
            await self._save_query_history(
                request, user_id, execution_time, 
                None, None, "failed", error_msg
            )
            
            raise e

    async def _execute_mysql_query(self, connection: DatabaseConnection, request: QueryExecuteRequest) -> QueryResult:
        """执行MySQL查询"""
        conn = await aiomysql.connect(
            host=connection.host,
            port=connection.port,
            user=connection.username,
            password=connection.password,
            db=connection.database,
            charset=connection.charset
        )
        
        try:
            async with conn.cursor() as cursor:
                await cursor.execute(request.sql_content)
                
                if cursor.description:
                    # SELECT查询
                    columns = [desc[0] for desc in cursor.description]
                    if request.limit:
                        data = await cursor.fetchmany(request.limit)
                    else:
                        data = await cursor.fetchall()
                    
                    # 转换为列表格式并序列化
                    data = [list(row) for row in data]
                    data = serialize_row_data(data)

                    return QueryResult(
                        columns=columns,
                        data=data,
                        total_count=len(data),
                        execution_time=0,  # 将在上层设置
                        affected_rows=None
                    )
                else:
                    # INSERT/UPDATE/DELETE查询
                    await conn.commit()
                    return QueryResult(
                        columns=[],
                        data=[],
                        total_count=0,
                        execution_time=0,  # 将在上层设置
                        affected_rows=cursor.rowcount
                    )
        finally:
            await conn.ensure_closed()

    async def _execute_postgresql_query(self, connection: DatabaseConnection, request: QueryExecuteRequest) -> QueryResult:
        """执行PostgreSQL查询"""
        conn = await asyncpg.connect(
            host=connection.host,
            port=connection.port,
            user=connection.username,
            password=connection.password,
            database=connection.database
        )
        
        try:
            if request.sql_content.strip().upper().startswith('SELECT'):
                # SELECT查询
                if request.limit:
                    sql_with_limit = f"{request.sql_content} LIMIT {request.limit}"
                    rows = await conn.fetch(sql_with_limit)
                else:
                    rows = await conn.fetch(request.sql_content)
                
                if rows:
                    columns = list(rows[0].keys())
                    data = [list(row.values()) for row in rows]
                    data = serialize_row_data(data)
                else:
                    columns = []
                    data = []

                return QueryResult(
                    columns=columns,
                    data=data,
                    total_count=len(data),
                    execution_time=0,
                    affected_rows=None
                )
            else:
                # INSERT/UPDATE/DELETE查询
                result = await conn.execute(request.sql_content)
                # PostgreSQL返回的结果格式如 "UPDATE 5"
                affected_rows = int(result.split()[-1]) if result.split()[-1].isdigit() else 0
                
                return QueryResult(
                    columns=[],
                    data=[],
                    total_count=0,
                    execution_time=0,
                    affected_rows=affected_rows
                )
        finally:
            await conn.close()

    async def _execute_sqlite_query(self, connection: DatabaseConnection, request: QueryExecuteRequest) -> QueryResult:
        """执行SQLite查询"""
        async with aiosqlite.connect(connection.database) as conn:
            cursor = await conn.execute(request.sql_content)
            
            if cursor.description:
                # SELECT查询
                columns = [desc[0] for desc in cursor.description]
                if request.limit:
                    data = await cursor.fetchmany(request.limit)
                else:
                    data = await cursor.fetchall()
                
                data = [list(row) for row in data]
                data = serialize_row_data(data)

                return QueryResult(
                    columns=columns,
                    data=data,
                    total_count=len(data),
                    execution_time=0,
                    affected_rows=None
                )
            else:
                # INSERT/UPDATE/DELETE查询
                await conn.commit()
                return QueryResult(
                    columns=[],
                    data=[],
                    total_count=0,
                    execution_time=0,
                    affected_rows=cursor.rowcount
                )

    async def _save_query_history(
        self, request: QueryExecuteRequest, user_id: int, 
        execution_time: int, result_count: Optional[int], 
        affected_rows: Optional[int], status: str, error_message: Optional[str]
    ):
        """保存查询历史"""
        await QueryHistory.create(
            query_name=request.query_name,
            sql_content=request.sql_content,
            database_connection_id=request.database_connection_id,
            execution_time=execution_time,
            affected_rows=affected_rows,
            result_count=result_count,
            status=status,
            error_message=error_message,
            user_id=user_id
        )


    async def get_database_schema(self, connection_id: int, user_id: int) -> DatabaseSchema:
        """获取数据库表结构"""
        connection = await self.model.get(id=connection_id, user_id=user_id)

        try:
            if connection.db_type.lower() == "mysql":
                return await self._get_mysql_schema(connection)
            elif connection.db_type.lower() == "postgresql":
                return await self._get_postgresql_schema(connection)
            elif connection.db_type.lower() == "sqlite":
                return await self._get_sqlite_schema(connection)
            else:
                raise ValueError(f"不支持的数据库类型: {connection.db_type}")
        except Exception as e:
            logger.error(f"获取数据库表结构失败: {str(e)}")
            raise e

    async def _get_mysql_schema(self, connection: DatabaseConnection) -> DatabaseSchema:
        """获取MySQL数据库表结构"""
        conn = await aiomysql.connect(
            host=connection.host,
            port=connection.port,
            user=connection.username,
            password=connection.password,
            db=connection.database,
            charset=connection.charset
        )

        try:
            async with conn.cursor() as cursor:
                await cursor.execute("""
                    SELECT TABLE_NAME, TABLE_COMMENT, TABLE_TYPE
                    FROM INFORMATION_SCHEMA.TABLES
                    WHERE TABLE_SCHEMA = %s
                    ORDER BY TABLE_NAME
                """, (connection.database,))

                tables = []
                for row in await cursor.fetchall():
                    tables.append(TableInfo(
                        table_name=row[0],
                        table_comment=row[1] or "",
                        table_type=row[2]
                    ))

                return DatabaseSchema(tables=tables)
        finally:
            await conn.ensure_closed()

    async def _get_postgresql_schema(self, connection: DatabaseConnection) -> DatabaseSchema:
        """获取PostgreSQL数据库表结构"""
        conn = await asyncpg.connect(
            host=connection.host,
            port=connection.port,
            user=connection.username,
            password=connection.password,
            database=connection.database
        )

        try:
            rows = await conn.fetch("""
                SELECT table_name, '' as table_comment, table_type
                FROM information_schema.tables
                WHERE table_schema = 'public'
                ORDER BY table_name
            """)

            tables = []
            for row in rows:
                tables.append(TableInfo(
                    table_name=row['table_name'],
                    table_comment=row['table_comment'] or "",
                    table_type=row['table_type']
                ))

            return DatabaseSchema(tables=tables)
        finally:
            await conn.close()

    async def _get_sqlite_schema(self, connection: DatabaseConnection) -> DatabaseSchema:
        """获取SQLite数据库表结构"""
        async with aiosqlite.connect(connection.database) as conn:
            cursor = await conn.execute("""
                SELECT name, '', 'BASE TABLE' as table_type
                FROM sqlite_master
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            """)

            tables = []
            for row in await cursor.fetchall():
                tables.append(TableInfo(
                    table_name=row[0],
                    table_comment=row[1] or "",
                    table_type=row[2]
                ))

            return DatabaseSchema(tables=tables)

    async def get_table_schema(self, connection_id: int, table_name: str, user_id: int) -> TableSchema:
        """获取表的详细结构"""
        connection = await self.model.get(id=connection_id, user_id=user_id)

        try:
            if connection.db_type.lower() == "mysql":
                return await self._get_mysql_table_schema(connection, table_name)
            elif connection.db_type.lower() == "postgresql":
                return await self._get_postgresql_table_schema(connection, table_name)
            elif connection.db_type.lower() == "sqlite":
                return await self._get_sqlite_table_schema(connection, table_name)
            else:
                raise ValueError(f"不支持的数据库类型: {connection.db_type}")
        except Exception as e:
            logger.error(f"获取表结构失败: {str(e)}")
            raise e

    async def _get_mysql_table_schema(self, connection: DatabaseConnection, table_name: str) -> TableSchema:
        """获取MySQL表结构"""
        conn = await aiomysql.connect(
            host=connection.host,
            port=connection.port,
            user=connection.username,
            password=connection.password,
            db=connection.database,
            charset=connection.charset
        )

        try:
            async with conn.cursor() as cursor:
                await cursor.execute("""
                    SELECT
                        COLUMN_NAME,
                        DATA_TYPE,
                        IS_NULLABLE,
                        COLUMN_DEFAULT,
                        COLUMN_COMMENT,
                        CHARACTER_MAXIMUM_LENGTH,
                        COLUMN_KEY
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s
                    ORDER BY ORDINAL_POSITION
                """, (connection.database, table_name))

                columns = []
                for row in await cursor.fetchall():
                    columns.append(ColumnInfo(
                        column_name=row[0],
                        data_type=row[1],
                        is_nullable=row[2],
                        column_default=row[3],
                        column_comment=row[4] or "",
                        character_maximum_length=row[5],
                        is_primary_key=row[6] == 'PRI'
                    ))

                return TableSchema(table_name=table_name, columns=columns)
        finally:
            await conn.ensure_closed()

    async def get_query_history(self, user_id: int, page: int = 1, page_size: int = 20) -> Tuple[int, List[QueryHistory]]:
        """获取查询历史"""
        total, history_list = await QueryHistory.filter(user_id=user_id).order_by("-created_at").limit(page_size).offset((page - 1) * page_size), await QueryHistory.filter(user_id=user_id).count()
        return total, history_list

    async def toggle_favorite(self, history_id: int, user_id: int) -> bool:
        """切换收藏状态"""
        history = await QueryHistory.get(id=history_id, user_id=user_id)
        history.is_favorite = not history.is_favorite
        await history.save()
        return history.is_favorite


database_query_controller = DatabaseQueryController()
